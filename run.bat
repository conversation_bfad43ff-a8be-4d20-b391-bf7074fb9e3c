@echo off
echo Starting Beyond Smart Glass Tracker...
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please run install.bat first
    echo.
    pause
    exit /b 1
)

REM Run the application
python main.py

REM Keep window open if there's an error
if %errorlevel% neq 0 (
    echo.
    echo Application exited with an error
    pause
)
