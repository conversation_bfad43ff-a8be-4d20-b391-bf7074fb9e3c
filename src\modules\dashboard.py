"""
Sales Dashboard Module
"""

import tkinter as tk
from tkinter import ttk
from datetime import datetime, timedelta
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from database.models import DatabaseManager
except ImportError:
    import sqlite3

class DashboardModule:
    """Sales Dashboard UI"""
    
    def __init__(self, parent, db: DatabaseManager, current_user: dict):
        self.parent = parent
        self.db = db
        self.current_user = current_user
        
        self.create_interface()
    
    def create_interface(self):
        """Create dashboard interface"""
        # Main container
        main_frame = tk.Frame(self.parent, bg='white')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Header
        header_frame = tk.Frame(main_frame, bg='white')
        header_frame.pack(fill='x', pady=(0, 20))
        
        title_label = tk.Label(header_frame, text="Sales Dashboard", 
                              font=('Segoe UI', 18, 'bold'), bg='white', fg='#2C3E50')
        title_label.pack(side='left')
        
        # Date range selector
        date_frame = tk.Frame(header_frame, bg='white')
        date_frame.pack(side='right')
        
        tk.Label(date_frame, text="Period:", font=('Segoe UI', 10), bg='white').pack(side='left', padx=(0, 5))
        
        self.period_var = tk.StringVar(value="This Month")
        period_combo = ttk.Combobox(date_frame, textvariable=self.period_var,
                                   values=["This Week", "This Month", "This Quarter", "This Year"],
                                   state="readonly", width=12)
        period_combo.pack(side='left', padx=(0, 10))
        period_combo.bind('<<ComboboxSelected>>', lambda e: self.refresh_data())
        
        refresh_btn = tk.Button(date_frame, text="Refresh", command=self.refresh_data,
                               bg='#4A90E2', fg='white', font=('Segoe UI', 9, 'bold'),
                               relief='flat', padx=15, pady=5)
        refresh_btn.pack(side='left')
        
        # Statistics Cards Row
        stats_frame = tk.Frame(main_frame, bg='white')
        stats_frame.pack(fill='x', pady=(0, 20))
        
        # Create stat cards
        self.create_stat_card(stats_frame, "Total Quotations", "0", "#4A90E2", 0)
        self.create_stat_card(stats_frame, "Contracts Signed", "0", "#27AE60", 1)
        self.create_stat_card(stats_frame, "Installations Completed", "0", "#F39C12", 2)
        self.create_stat_card(stats_frame, "Total Revenue", "$0", "#E74C3C", 3)
        
        # Content area with two columns
        content_frame = tk.Frame(main_frame, bg='white')
        content_frame.pack(fill='both', expand=True)
        
        # Left column - Recent Activity
        left_frame = tk.Frame(content_frame, bg='white')
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        self.create_recent_activity_section(left_frame)
        
        # Right column - Quick Stats
        right_frame = tk.Frame(content_frame, bg='white')
        right_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        self.create_quick_stats_section(right_frame)
        
        # Load initial data
        self.refresh_data()
    
    def create_stat_card(self, parent, title, value, color, column):
        """Create a statistics card"""
        card_frame = tk.Frame(parent, bg='white', relief='solid', bd=1)
        card_frame.grid(row=0, column=column, sticky='ew', padx=5)
        parent.grid_columnconfigure(column, weight=1)
        
        # Title
        title_label = tk.Label(card_frame, text=title, font=('Segoe UI', 10), 
                              bg='white', fg='#7F8C8D')
        title_label.pack(pady=(15, 5))
        
        # Value
        value_label = tk.Label(card_frame, text=value, font=('Segoe UI', 20, 'bold'),
                              bg='white', fg=color)
        value_label.pack(pady=(0, 15))
        
        # Store reference for updating
        setattr(self, f"stat_{column}_label", value_label)
    
    def create_recent_activity_section(self, parent):
        """Create recent activity section"""
        # Section header
        header_frame = tk.Frame(parent, bg='white')
        header_frame.pack(fill='x', pady=(0, 10))
        
        tk.Label(header_frame, text="Recent Activity", font=('Segoe UI', 14, 'bold'),
                bg='white', fg='#2C3E50').pack(side='left')
        
        # Activity list frame
        list_frame = tk.Frame(parent, bg='white', relief='solid', bd=1)
        list_frame.pack(fill='both', expand=True)
        
        # Treeview for recent activities
        columns = ('Type', 'Description', 'Date')
        self.activity_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)
        
        # Configure columns
        self.activity_tree.heading('Type', text='Type')
        self.activity_tree.heading('Description', text='Description')
        self.activity_tree.heading('Date', text='Date')
        
        self.activity_tree.column('Type', width=100)
        self.activity_tree.column('Description', width=250)
        self.activity_tree.column('Date', width=100)
        
        # Scrollbar
        activity_scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.activity_tree.yview)
        self.activity_tree.configure(yscrollcommand=activity_scrollbar.set)
        
        # Pack
        self.activity_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        activity_scrollbar.pack(side='right', fill='y', pady=10)
    
    def create_quick_stats_section(self, parent):
        """Create quick statistics section"""
        # Section header
        header_frame = tk.Frame(parent, bg='white')
        header_frame.pack(fill='x', pady=(0, 10))
        
        tk.Label(header_frame, text="Quick Statistics", font=('Segoe UI', 14, 'bold'),
                bg='white', fg='#2C3E50').pack(side='left')
        
        # Stats container
        stats_container = tk.Frame(parent, bg='white', relief='solid', bd=1)
        stats_container.pack(fill='both', expand=True, padx=0, pady=0)
        
        # Status breakdown
        self.create_status_breakdown(stats_container)
        
        # Low stock alerts
        self.create_low_stock_alerts(stats_container)
    
    def create_status_breakdown(self, parent):
        """Create quotation status breakdown"""
        status_frame = tk.LabelFrame(parent, text="Quotation Status", font=('Segoe UI', 10, 'bold'),
                                    bg='white', fg='#2C3E50', padx=10, pady=10)
        status_frame.pack(fill='x', padx=10, pady=10)
        
        # Status items
        statuses = [
            ("Draft", "#95A5A6"),
            ("Sent", "#3498DB"),
            ("Accepted", "#27AE60"),
            ("Rejected", "#E74C3C")
        ]
        
        self.status_labels = {}
        for i, (status, color) in enumerate(statuses):
            row_frame = tk.Frame(status_frame, bg='white')
            row_frame.pack(fill='x', pady=2)
            
            tk.Label(row_frame, text=status, font=('Segoe UI', 9), bg='white', fg='#2C3E50').pack(side='left')
            
            count_label = tk.Label(row_frame, text="0", font=('Segoe UI', 9, 'bold'), bg='white', fg=color)
            count_label.pack(side='right')
            
            self.status_labels[status] = count_label
    
    def create_low_stock_alerts(self, parent):
        """Create low stock alerts section"""
        stock_frame = tk.LabelFrame(parent, text="Low Stock Alerts", font=('Segoe UI', 10, 'bold'),
                                   bg='white', fg='#2C3E50', padx=10, pady=10)
        stock_frame.pack(fill='x', padx=10, pady=10)
        
        # Stock alerts list
        self.stock_listbox = tk.Listbox(stock_frame, height=6, font=('Segoe UI', 9),
                                       bg='white', fg='#2C3E50', selectbackground='#EBF3FD')
        self.stock_listbox.pack(fill='x', pady=5)
    
    def refresh_data(self):
        """Refresh dashboard data"""
        try:
            # Get date range based on selected period
            end_date = datetime.now()
            period = self.period_var.get()
            
            if period == "This Week":
                start_date = end_date - timedelta(days=7)
            elif period == "This Month":
                start_date = end_date.replace(day=1)
            elif period == "This Quarter":
                quarter_start_month = ((end_date.month - 1) // 3) * 3 + 1
                start_date = end_date.replace(month=quarter_start_month, day=1)
            else:  # This Year
                start_date = end_date.replace(month=1, day=1)
            
            start_date_str = start_date.strftime('%Y-%m-%d')
            end_date_str = end_date.strftime('%Y-%m-%d')
            
            # Update statistics
            self.update_statistics(start_date_str, end_date_str)
            
            # Update recent activity
            self.update_recent_activity()
            
            # Update status breakdown
            self.update_status_breakdown()
            
            # Update low stock alerts
            self.update_low_stock_alerts()
            
        except Exception as e:
            print(f"Error refreshing dashboard data: {e}")
    
    def update_statistics(self, start_date, end_date):
        """Update main statistics"""
        try:
            # Total quotations
            quotations = self.db.execute_query(
                "SELECT COUNT(*) as count FROM quotations WHERE created_at >= ? AND created_at <= ?",
                (start_date, end_date + ' 23:59:59')
            )
            total_quotations = quotations[0]['count'] if quotations else 0
            self.stat_0_label.config(text=str(total_quotations))
            
            # Contracts signed
            contracts = self.db.execute_query(
                """SELECT COUNT(*) as count FROM contracts c 
                   JOIN quotations q ON c.quotation_id = q.id 
                   WHERE c.signature_status = 'Signed' AND q.created_at >= ? AND q.created_at <= ?""",
                (start_date, end_date + ' 23:59:59')
            )
            signed_contracts = contracts[0]['count'] if contracts else 0
            self.stat_1_label.config(text=str(signed_contracts))
            
            # Installations completed
            installations = self.db.execute_query(
                """SELECT COUNT(*) as count FROM installations i
                   JOIN contracts c ON i.contract_id = c.id
                   JOIN quotations q ON c.quotation_id = q.id
                   WHERE i.status = 'Completed' AND q.created_at >= ? AND q.created_at <= ?""",
                (start_date, end_date + ' 23:59:59')
            )
            completed_installations = installations[0]['count'] if installations else 0
            self.stat_2_label.config(text=str(completed_installations))
            
            # Total revenue
            revenue = self.db.execute_query(
                """SELECT SUM(q.total_amount) as total FROM quotations q
                   JOIN contracts c ON q.id = c.quotation_id
                   WHERE c.signature_status = 'Signed' AND q.created_at >= ? AND q.created_at <= ?""",
                (start_date, end_date + ' 23:59:59')
            )
            total_revenue = revenue[0]['total'] if revenue and revenue[0]['total'] else 0
            self.stat_3_label.config(text=f"${total_revenue:,.2f}")
            
        except Exception as e:
            print(f"Error updating statistics: {e}")
    
    def update_recent_activity(self):
        """Update recent activity list"""
        try:
            # Clear existing items
            for item in self.activity_tree.get_children():
                self.activity_tree.delete(item)
            
            # Get recent quotations
            recent_quotations = self.db.execute_query(
                "SELECT client_name, created_at FROM quotations ORDER BY created_at DESC LIMIT 5"
            )
            
            for quotation in recent_quotations:
                date_str = quotation['created_at'][:10] if quotation['created_at'] else ""
                self.activity_tree.insert('', 'end', values=(
                    'Quotation',
                    f"New quotation for {quotation['client_name']}",
                    date_str
                ))
            
            # Get recent contracts
            recent_contracts = self.db.execute_query(
                """SELECT q.client_name, c.created_at FROM contracts c
                   JOIN quotations q ON c.quotation_id = q.id
                   ORDER BY c.created_at DESC LIMIT 3"""
            )
            
            for contract in recent_contracts:
                date_str = contract['created_at'][:10] if contract['created_at'] else ""
                self.activity_tree.insert('', 'end', values=(
                    'Contract',
                    f"Contract signed for {contract['client_name']}",
                    date_str
                ))
                
        except Exception as e:
            print(f"Error updating recent activity: {e}")
    
    def update_status_breakdown(self):
        """Update quotation status breakdown"""
        try:
            statuses = ["Draft", "Sent", "Accepted", "Rejected"]
            
            for status in statuses:
                count = self.db.execute_query(
                    "SELECT COUNT(*) as count FROM quotations WHERE status = ?",
                    (status,)
                )
                count_value = count[0]['count'] if count else 0
                if status in self.status_labels:
                    self.status_labels[status].config(text=str(count_value))
                    
        except Exception as e:
            print(f"Error updating status breakdown: {e}")
    
    def update_low_stock_alerts(self):
        """Update low stock alerts"""
        try:
            # Clear existing items
            self.stock_listbox.delete(0, tk.END)
            
            # Get low stock items
            low_stock = self.db.execute_query(
                "SELECT item_name, current_stock, min_stock_alert FROM inventory WHERE current_stock <= min_stock_alert"
            )
            
            if low_stock:
                for item in low_stock:
                    self.stock_listbox.insert(tk.END, f"{item['item_name']}: {item['current_stock']} left")
            else:
                self.stock_listbox.insert(tk.END, "No low stock alerts")
                
        except Exception as e:
            print(f"Error updating low stock alerts: {e}")
