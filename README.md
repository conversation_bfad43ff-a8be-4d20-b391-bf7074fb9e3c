# Beyond Smart Glass Tracker

A comprehensive Windows desktop application for managing smart glass business operations, built with Python and Tkinter.

## Features

✅ **Complete Business Management System**
- User authentication with role-based access (Admin/Sales)
- Quotations management with auto-calculation and PDF export
- Contract management and tracking
- Manufacturing progress tracking
- Installation scheduling
- Inventory management with low stock alerts
- Sales dashboard with visual statistics
- Admin panel for user and item type management

✅ **Modern UI Design**
- Soft color palette (white and light blue)
- Modern flat design with clean layouts
- Professional typography (Segoe UI)
- Responsive interface optimized for Windows

✅ **Offline Operation**
- SQLite database for local data storage
- No internet connection required
- Fast and reliable performance

## Installation Requirements

### 1. Install Python 3.8 or higher
Download and install Python from [python.org](https://www.python.org/downloads/)

**Important:** During installation, make sure to check "Add Python to PATH"

### 2. Install Required Dependencies
Open Command Prompt or PowerShell and run:

```bash
pip install customtkinter Pillow reportlab pandas matplotlib tkcalendar
```

### 3. Download the Application
Clone or download this repository to your desired location.

## Running the Application

1. Open Command Prompt or PowerShell
2. Navigate to the application directory:
   ```bash
   cd "path\to\Beyond Smart Glass"
   ```
3. Run the application:
   ```bash
   python main.py
   ```

## Default Login Credentials

- **Username:** admin
- **Password:** admin123
- **Role:** Administrator

## Application Modules

### 1. Dashboard
- Overview of business metrics
- Recent activity tracking
- Quick statistics and status breakdown
- Low stock alerts

### 2. Quotations Management
- Create and edit quotations
- Client information management
- Item type selection with multi-select
- Auto-calculation of totals with discount support
- PDF export functionality
- Status tracking (Draft/Sent/Accepted/Rejected)

### 3. Contracts Management
- Convert accepted quotations to contracts
- Contract number generation
- Signature status tracking
- Contract details management

### 4. Manufacturing Tracker
- Production status tracking (Pending/In Progress/Completed)
- Cutting instructions and notes
- Timeline management

### 5. Installation Scheduler
- Team assignment
- Installation date scheduling
- Completion status tracking
- Project notes

### 6. Inventory Manager
- Stock tracking for Smart Film Rolls, Devices, and Remotes
- Add/remove stock with quantity logs
- Low stock alerts and notifications
- Transaction history

### 7. Admin Panel (Admin users only)
- User management (create, edit, activate/deactivate)
- Item type management
- System configuration

## Database Schema

The application uses SQLite with the following main tables:
- `users` - User accounts and authentication
- `quotations` - Customer quotations
- `quotation_items` - Items included in quotations
- `contracts` - Contract management
- `manufacturing` - Production tracking
- `installations` - Installation scheduling
- `inventory` - Stock management
- `inventory_transactions` - Stock transaction history
- `item_types` - Product/service categories

## Data Management

### Export/Import
- CSV export for all major data types
- PDF generation for quotations and contracts
- Data backup and restore functionality

### Backup
Regular backups are recommended. The database file `beyond_smart_glass.db` contains all application data.

## Troubleshooting

### Common Issues

1. **"Python was not found" error**
   - Ensure Python is installed and added to PATH
   - Try using `python3` instead of `python`

2. **Module import errors**
   - Install required dependencies using pip
   - Ensure you're in the correct directory

3. **Database errors**
   - Check file permissions in the application directory
   - Ensure the database file is not corrupted

### Support

For technical support or feature requests, please contact the development team.

## Development

### Project Structure
```
Beyond Smart Glass/
├── main.py                 # Application entry point
├── requirements.txt        # Python dependencies
├── src/
│   ├── app.py             # Main application class
│   ├── database/
│   │   └── models.py      # Database management
│   ├── modules/
│   │   ├── auth.py        # Authentication system
│   │   ├── dashboard.py   # Sales dashboard
│   │   ├── quotations.py  # Quotations management
│   │   └── ...           # Other modules
│   ├── ui/
│   │   ├── styles.py      # UI styling constants
│   │   └── base_widgets.py # Custom UI components
│   └── utils/
│       └── pdf_export.py  # PDF generation utilities
└── README.md
```

### Adding New Features
1. Create new modules in `src/modules/`
2. Update the navigation in `src/app.py`
3. Add database tables in `src/database/models.py`
4. Test thoroughly before deployment

## License

This software is proprietary and confidential. All rights reserved.

## Version History

- **v1.0.0** - Initial release with core functionality
- Complete quotations and contracts management
- User authentication and role-based access
- Dashboard and reporting features
- Inventory management system
