"""
PDF Export Utilities
"""

import os
from datetime import datetime
from tkinter import filedialog, messagebox

try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

class PDFExporter:
    """Handles PDF export functionality"""
    
    @staticmethod
    def export_quotation(quotation_data):
        """Export quotation to PDF"""
        if not REPORTLAB_AVAILABLE:
            messagebox.showerror("Error", "ReportLab library is required for PDF export.\nPlease install it using: pip install reportlab")
            return
        
        try:
            # Ask user for save location
            filename = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf")],
                title="Save Quotation PDF",
                initialname=f"Quotation_{quotation_data['id']}_{quotation_data['client_name'].replace(' ', '_')}.pdf"
            )
            
            if not filename:
                return
            
            # Create PDF
            doc = SimpleDocTemplate(filename, pagesize=A4)
            styles = getSampleStyleSheet()
            story = []
            
            # Company Header
            company_style = ParagraphStyle(
                'CompanyHeader',
                parent=styles['Heading1'],
                fontSize=24,
                textColor=colors.HexColor('#4A90E2'),
                alignment=1  # Center
            )
            
            story.append(Paragraph("Beyond Smart Glass", company_style))
            story.append(Spacer(1, 0.2*inch))
            
            # Quotation Title
            title_style = ParagraphStyle(
                'QuotationTitle',
                parent=styles['Heading2'],
                fontSize=18,
                alignment=1
            )
            
            story.append(Paragraph(f"QUOTATION #{quotation_data['id']}", title_style))
            story.append(Spacer(1, 0.3*inch))
            
            # Date and Status
            date_created = datetime.strptime(quotation_data['created_at'][:19], '%Y-%m-%d %H:%M:%S').strftime('%B %d, %Y')
            
            info_data = [
                ['Date:', date_created],
                ['Status:', quotation_data['status']],
                ['Created by:', quotation_data.get('created_by_name', 'N/A')]
            ]
            
            info_table = Table(info_data, colWidths=[1.5*inch, 3*inch])
            info_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ]))
            
            story.append(info_table)
            story.append(Spacer(1, 0.3*inch))
            
            # Client Information
            story.append(Paragraph("CLIENT INFORMATION", styles['Heading3']))
            story.append(Spacer(1, 0.1*inch))
            
            client_data = [
                ['Client Name:', quotation_data['client_name']],
                ['Phone:', quotation_data.get('phone', 'N/A')],
                ['Email:', quotation_data.get('email', 'N/A')],
                ['City:', quotation_data.get('city', 'N/A')]
            ]
            
            client_table = Table(client_data, colWidths=[1.5*inch, 4*inch])
            client_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ]))
            
            story.append(client_table)
            story.append(Spacer(1, 0.3*inch))
            
            # Project Details
            story.append(Paragraph("PROJECT DETAILS", styles['Heading3']))
            story.append(Spacer(1, 0.1*inch))
            
            # Item types
            item_names = [item['item_name'] for item in quotation_data.get('items', [])]
            items_text = ', '.join(item_names) if item_names else 'N/A'
            
            project_data = [
                ['Item Types:', items_text],
                ['Area (sqm):', f"{quotation_data['area_sqm']:.1f}"],
                ['Number of Devices:', str(quotation_data['num_devices'])],
                ['Price per sqm:', f"${quotation_data['price_per_sqm']:.2f}"]
            ]
            
            project_table = Table(project_data, colWidths=[1.5*inch, 4*inch])
            project_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ]))
            
            story.append(project_table)
            story.append(Spacer(1, 0.3*inch))
            
            # Pricing Summary
            story.append(Paragraph("PRICING SUMMARY", styles['Heading3']))
            story.append(Spacer(1, 0.1*inch))
            
            subtotal = quotation_data['area_sqm'] * quotation_data['price_per_sqm']
            discount_amount = subtotal * (quotation_data['discount'] / 100)
            
            pricing_data = [
                ['Subtotal:', f"${subtotal:.2f}"],
                ['Discount ({}%):'.format(quotation_data['discount']), f"-${discount_amount:.2f}"],
                ['', ''],
                ['TOTAL:', f"${quotation_data['total_amount']:.2f}"]
            ]
            
            pricing_table = Table(pricing_data, colWidths=[3*inch, 2*inch])
            pricing_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                ('FONTNAME', (0, 0), (0, -2), 'Helvetica'),
                ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -2), 10),
                ('FONTSIZE', (0, -1), (-1, -1), 14),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ('LINEABOVE', (0, -1), (-1, -1), 2, colors.black),
                ('TEXTCOLOR', (0, -1), (-1, -1), colors.HexColor('#4A90E2')),
            ]))
            
            story.append(pricing_table)
            story.append(Spacer(1, 0.5*inch))
            
            # Footer
            footer_text = """
            <para align="center">
            <font size="8">
            This quotation is valid for 30 days from the date of issue.<br/>
            For any questions, please contact <NAME_EMAIL><br/>
            Thank you for choosing Beyond Smart Glass!
            </font>
            </para>
            """
            
            story.append(Paragraph(footer_text, styles['Normal']))
            
            # Build PDF
            doc.build(story)
            
            messagebox.showinfo("Success", f"Quotation PDF exported successfully to:\n{filename}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export PDF: {str(e)}")
    
    @staticmethod
    def export_contract(contract_data):
        """Export contract to PDF"""
        if not REPORTLAB_AVAILABLE:
            messagebox.showerror("Error", "ReportLab library is required for PDF export.\nPlease install it using: pip install reportlab")
            return
        
        # Similar implementation for contracts
        messagebox.showinfo("Info", "Contract PDF export will be implemented in the next version")
    
    @staticmethod
    def export_inventory_report(inventory_data):
        """Export inventory report to PDF"""
        if not REPORTLAB_AVAILABLE:
            messagebox.showerror("Error", "ReportLab library is required for PDF export.\nPlease install it using: pip install reportlab")
            return
        
        # Similar implementation for inventory reports
        messagebox.showinfo("Info", "Inventory report PDF export will be implemented in the next version")
