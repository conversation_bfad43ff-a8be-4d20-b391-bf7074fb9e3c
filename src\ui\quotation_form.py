"""
Quotation Form Dialog for Beyond Smart Glass Tracker
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
from src.ui.styles import COLORS, FONTS, BUTTON_STYLE, BUTTON_SECONDARY_STYLE, ENTRY_STYLE, LABEL_STYLE
from src.utils.language import language_manager, _
from src.utils.dropdown_manager import dropdown_manager


class QuotationForm:
    def __init__(self, parent, callback=None):
        self.parent = parent
        self.callback = callback
        self.dialog = None
        self.create_dialog()
        
    def create_dialog(self):
        """Create the quotation form dialog"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(_('add_new_quotation'))
        self.dialog.geometry("600x700")
        self.dialog.configure(bg=COLORS['background'])
        self.dialog.resizable(False, False)
        
        # Center the dialog
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # Main container with padding
        main_frame = tk.Frame(self.dialog, bg=COLORS['background'])
        main_frame.pack(fill='both', expand=True, padx=30, pady=20)
        
        # Header
        header_frame = tk.Frame(main_frame, bg=COLORS['background'])
        header_frame.pack(fill='x', pady=(0, 30))
        
        title_label = tk.Label(
            header_frame,
            text=_('add_new_quotation'),
            font=FONTS['heading'],
            bg=COLORS['background'],
            fg=COLORS['text_primary']
        )
        title_label.pack()
        
        # Form container
        form_frame = tk.Frame(main_frame, bg=COLORS['surface'], relief='flat', bd=0)
        form_frame.pack(fill='both', expand=True, pady=(0, 20))
        form_frame.configure(highlightbackground=COLORS['border'], highlightthickness=1)
        
        # Form content with padding
        content_frame = tk.Frame(form_frame, bg=COLORS['surface'])
        content_frame.pack(fill='both', expand=True, padx=30, pady=30)
        
        # Form fields
        self.create_form_fields(content_frame)
        
        # Buttons
        self.create_buttons(main_frame)
        
    def create_form_fields(self, parent):
        """Create form input fields"""
        # Client Name
        self.create_field(parent, "اسم العميل:", "client_name", 0)
        
        # Phone
        self.create_field(parent, "رقم الهاتف:", "phone", 1)
        
        # City
        self.create_field(parent, "المدينة:", "city", 2)
        
        # Project Type
        self.create_dropdown_field(parent, "نوع المشروع:", "project_type", 3, 
                                 ["نوافذ", "أبواب", "واجهات", "مطابخ", "حمامات", "أخرى"])
        
        # Glass Type
        self.create_dropdown_field(parent, "نوع الزجاج:", "glass_type", 4,
                                 ["زجاج عادي", "زجاج مقسى", "زجاج مزدوج", "زجاج ذكي", "زجاج ملون"])
        
        # Area (Square meters)
        self.create_field(parent, "المساحة (متر مربع):", "area", 5, field_type="number")
        
        # Unit Price
        self.create_field(parent, "سعر المتر (ريال):", "unit_price", 6, field_type="number")
        
        # Total Amount (calculated automatically)
        self.create_field(parent, "المبلغ الإجمالي (ريال):", "total_amount", 7, field_type="number", readonly=True)
        
        # Notes
        self.create_text_field(parent, "ملاحظات:", "notes", 8)
        
        # Bind calculation events
        self.entries['area'].bind('<KeyRelease>', self.calculate_total)
        self.entries['unit_price'].bind('<KeyRelease>', self.calculate_total)
        
    def create_field(self, parent, label_text, field_name, row, field_type="text", readonly=False):
        """Create a form field with label and entry"""
        # Label
        label = tk.Label(
            parent,
            text=label_text,
            font=FONTS['default'],
            bg=COLORS['surface'],
            fg=COLORS['text_primary'],
            anchor='e'
        )
        label.grid(row=row, column=0, sticky='e', padx=(0, 15), pady=8)
        
        # Entry
        if not hasattr(self, 'entries'):
            self.entries = {}
            
        entry = tk.Entry(
            parent,
            font=FONTS['default'],
            bg='white' if not readonly else COLORS['secondary'],
            fg=COLORS['text_primary'],
            relief='solid',
            bd=1,
            highlightthickness=2,
            highlightcolor=COLORS['primary'],
            highlightbackground=COLORS['border'],
            state='readonly' if readonly else 'normal'
        )
        entry.grid(row=row, column=1, sticky='ew', pady=8)
        
        self.entries[field_name] = entry
        
        # Configure grid weights
        parent.grid_columnconfigure(1, weight=1)
        
    def create_dropdown_field(self, parent, label_text, field_name, row, options):
        """Create a dropdown field"""
        # Label
        label = tk.Label(
            parent,
            text=label_text,
            font=FONTS['default'],
            bg=COLORS['surface'],
            fg=COLORS['text_primary'],
            anchor='e'
        )
        label.grid(row=row, column=0, sticky='e', padx=(0, 15), pady=8)
        
        # Combobox
        if not hasattr(self, 'entries'):
            self.entries = {}

        # Configure combobox style
        style = ttk.Style()
        style.configure("TCombobox",
                       fieldbackground='white',
                       background='white',
                       foreground=COLORS['text_primary'],
                       borderwidth=1,
                       relief='solid')

        combo = ttk.Combobox(
            parent,
            font=FONTS['default'],
            values=options,
            state='readonly'
        )
        combo.grid(row=row, column=1, sticky='ew', pady=8)

        self.entries[field_name] = combo
        
    def create_text_field(self, parent, label_text, field_name, row):
        """Create a text area field"""
        # Label
        label = tk.Label(
            parent,
            text=label_text,
            font=FONTS['default'],
            bg=COLORS['surface'],
            fg=COLORS['text_primary'],
            anchor='ne'
        )
        label.grid(row=row, column=0, sticky='ne', padx=(0, 15), pady=8)
        
        # Text widget
        if not hasattr(self, 'entries'):
            self.entries = {}
            
        text_widget = tk.Text(
            parent,
            font=FONTS['default'],
            bg='white',
            fg=COLORS['text_primary'],
            relief='solid',
            bd=1,
            highlightthickness=2,
            highlightcolor=COLORS['primary'],
            highlightbackground=COLORS['border'],
            height=4,
            wrap='word'
        )
        text_widget.grid(row=row, column=1, sticky='ew', pady=8)
        
        self.entries[field_name] = text_widget
        
    def create_buttons(self, parent):
        """Create action buttons"""
        button_frame = tk.Frame(parent, bg=COLORS['background'])
        button_frame.pack(fill='x', pady=(10, 0))
        
        # Cancel button
        cancel_btn = tk.Button(
            button_frame,
            text="إلغاء",
            command=self.cancel,
            **BUTTON_SECONDARY_STYLE
        )
        cancel_btn.pack(side='left', padx=(0, 10))
        
        # Save button
        save_btn = tk.Button(
            button_frame,
            text="حفظ العرض",
            command=self.save_quotation,
            **BUTTON_STYLE
        )
        save_btn.pack(side='right')
        
    def calculate_total(self, event=None):
        """Calculate total amount automatically"""
        try:
            area = float(self.entries['area'].get() or 0)
            unit_price = float(self.entries['unit_price'].get() or 0)
            total = area * unit_price
            
            self.entries['total_amount'].config(state='normal')
            self.entries['total_amount'].delete(0, tk.END)
            self.entries['total_amount'].insert(0, f"{total:.2f}")
            self.entries['total_amount'].config(state='readonly')
        except ValueError:
            pass
            
    def save_quotation(self):
        """Save the quotation"""
        # Validate required fields
        required_fields = ['client_name', 'phone', 'city', 'project_type', 'glass_type']
        
        for field in required_fields:
            value = self.entries[field].get().strip()
            if not value:
                messagebox.showerror("خطأ", f"يرجى ملء جميع الحقول المطلوبة")
                return
                
        # Get form data
        data = {}
        for field_name, widget in self.entries.items():
            if isinstance(widget, tk.Text):
                data[field_name] = widget.get('1.0', tk.END).strip()
            else:
                data[field_name] = widget.get().strip()
                
        # Add timestamp
        data['created_date'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        data['status'] = "جديد"
        
        # Call callback if provided
        if self.callback:
            self.callback(data)
            
        messagebox.showinfo("نجح", "تم حفظ العرض بنجاح!")
        self.dialog.destroy()
        
    def cancel(self):
        """Cancel and close dialog"""
        self.dialog.destroy()
        
    def show(self):
        """Show the dialog"""
        if self.dialog:
            self.dialog.focus()
