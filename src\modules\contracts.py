"""
Contracts Management Module
"""

import tkinter as tk
from tkinter import messagebox, ttk
from datetime import datetime
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from database.models import DatabaseManager
except ImportError:
    import sqlite3

class ContractsManager:
    """Handles contract operations"""
    
    def __init__(self, db: DatabaseManager):
        self.db = db
    
    def create_contract_from_quotation(self, quotation_id: int) -> int:
        """Create a contract from an accepted quotation"""
        try:
            # Check if quotation exists and is accepted
            quotations = self.db.execute_query(
                "SELECT * FROM quotations WHERE id = ? AND status = 'Accepted'",
                (quotation_id,)
            )
            
            if not quotations:
                messagebox.showerror("Error", "Quotation not found or not accepted")
                return None
            
            # Check if contract already exists
            existing = self.db.execute_query(
                "SELECT id FROM contracts WHERE quotation_id = ?",
                (quotation_id,)
            )
            
            if existing:
                messagebox.showerror("Error", "Contract already exists for this quotation")
                return None
            
            # Generate contract number
            contract_number = self.generate_contract_number()
            
            # Create contract
            contract_id = self.db.get_last_insert_id(
                """INSERT INTO contracts 
                   (quotation_id, contract_number, contract_date)
                   VALUES (?, ?, ?)""",
                (quotation_id, contract_number, datetime.now().date())
            )
            
            return contract_id
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to create contract: {str(e)}")
            return None
    
    def generate_contract_number(self) -> str:
        """Generate a unique contract number"""
        # Get current year
        year = datetime.now().year
        
        # Get count of contracts this year
        count = self.db.execute_query(
            "SELECT COUNT(*) as count FROM contracts WHERE contract_date >= ?",
            (f"{year}-01-01",)
        )
        
        next_number = (count[0]['count'] if count else 0) + 1
        
        return f"BSG-{year}-{next_number:04d}"
    
    def update_contract_signature(self, contract_id: int, status: str, signed_date: str = None) -> bool:
        """Update contract signature status"""
        try:
            if status == 'Signed' and not signed_date:
                signed_date = datetime.now().date()
            
            self.db.execute_update(
                "UPDATE contracts SET signature_status = ?, signed_date = ? WHERE id = ?",
                (status, signed_date, contract_id)
            )
            return True
        except Exception as e:
            messagebox.showerror("Error", f"Failed to update signature status: {str(e)}")
            return False
    
    def get_contracts(self, status_filter=None) -> list:
        """Get all contracts with optional status filter"""
        query = """
            SELECT c.*, q.client_name, q.phone, q.email, q.city, q.total_amount,
                   u.full_name as created_by_name
            FROM contracts c
            JOIN quotations q ON c.quotation_id = q.id
            LEFT JOIN users u ON q.created_by = u.id
        """
        params = ()
        
        if status_filter:
            query += " WHERE c.signature_status = ?"
            params = (status_filter,)
        
        query += " ORDER BY c.created_at DESC"
        
        return self.db.execute_query(query, params)
    
    def get_contract_by_id(self, contract_id: int) -> dict:
        """Get contract by ID with full details"""
        contracts = self.db.execute_query(
            """SELECT c.*, q.client_name, q.phone, q.email, q.city, q.area_sqm,
                      q.num_devices, q.price_per_sqm, q.discount, q.total_amount,
                      u.full_name as created_by_name
               FROM contracts c
               JOIN quotations q ON c.quotation_id = q.id
               LEFT JOIN users u ON q.created_by = u.id
               WHERE c.id = ?""",
            (contract_id,)
        )
        
        if not contracts:
            return None
        
        contract = contracts[0]
        
        # Get quotation items
        items = self.db.execute_query(
            """SELECT qi.*, it.name as item_name
               FROM quotation_items qi
               JOIN item_types it ON qi.item_type_id = it.id
               WHERE qi.quotation_id = ?""",
            (contract['quotation_id'],)
        )
        
        contract['items'] = items
        return contract
    
    def get_accepted_quotations_without_contracts(self) -> list:
        """Get accepted quotations that don't have contracts yet"""
        return self.db.execute_query(
            """SELECT q.* FROM quotations q
               LEFT JOIN contracts c ON q.id = c.quotation_id
               WHERE q.status = 'Accepted' AND c.id IS NULL
               ORDER BY q.created_at DESC"""
        )

class ContractsModule:
    """Contracts module UI"""
    
    def __init__(self, parent, db: DatabaseManager, current_user: dict):
        self.parent = parent
        self.db = db
        self.current_user = current_user
        self.contracts_manager = ContractsManager(db)
        self.contracts_tree = None
        self.status_filter_var = None
        
        self.create_interface()
    
    def create_interface(self):
        """Create contracts interface"""
        # Header
        header_frame = tk.Frame(self.parent, bg='white')
        header_frame.pack(fill='x', padx=20, pady=(20, 10))
        
        title_label = tk.Label(header_frame, text="Contracts Management", 
                              font=('Segoe UI', 16, 'bold'), bg='white', fg='#2C3E50')
        title_label.pack(side='left')
        
        # Header buttons
        btn_frame = tk.Frame(header_frame, bg='white')
        btn_frame.pack(side='right')
        
        new_btn = tk.Button(btn_frame, text="Create from Quotation", 
                           command=self.create_from_quotation,
                           bg='#4A90E2', fg='white', font=('Segoe UI', 10, 'bold'),
                           relief='flat', padx=15, pady=5)
        new_btn.pack(side='left', padx=(0, 10))
        
        refresh_btn = tk.Button(btn_frame, text="Refresh", command=self.load_contracts,
                               bg='#F8F9FA', fg='#2C3E50', font=('Segoe UI', 10, 'bold'),
                               relief='flat', padx=15, pady=5)
        refresh_btn.pack(side='left')
        
        # Filters
        filter_frame = tk.Frame(self.parent, bg='white')
        filter_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        tk.Label(filter_frame, text="Status Filter:", font=('Segoe UI', 10), bg='white').pack(side='left', padx=(0, 10))
        
        self.status_filter_var = tk.StringVar(value="All")
        status_combo = ttk.Combobox(filter_frame, textvariable=self.status_filter_var,
                                   values=["All", "Pending", "Signed", "Cancelled"],
                                   state="readonly", width=15)
        status_combo.pack(side='left', padx=(0, 10))
        status_combo.bind('<<ComboboxSelected>>', lambda e: self.load_contracts())
        
        # Contracts list
        list_frame = tk.Frame(self.parent, bg='white', relief='solid', bd=1)
        list_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Treeview
        columns = ('ID', 'Contract #', 'Client', 'Phone', 'Total', 'Signature Status', 'Contract Date', 'Signed Date')
        self.contracts_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        for col in columns:
            self.contracts_tree.heading(col, text=col)
        
        # Column widths
        self.contracts_tree.column('ID', width=50)
        self.contracts_tree.column('Contract #', width=120)
        self.contracts_tree.column('Client', width=150)
        self.contracts_tree.column('Phone', width=120)
        self.contracts_tree.column('Total', width=100)
        self.contracts_tree.column('Signature Status', width=100)
        self.contracts_tree.column('Contract Date', width=100)
        self.contracts_tree.column('Signed Date', width=100)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.contracts_tree.yview)
        self.contracts_tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack
        self.contracts_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        scrollbar.pack(side='right', fill='y', pady=10)
        
        # Buttons
        buttons_frame = tk.Frame(self.parent, bg='white')
        buttons_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        view_btn = tk.Button(buttons_frame, text="View Details", command=self.view_contract,
                            bg='#4A90E2', fg='white', font=('Segoe UI', 10, 'bold'),
                            relief='flat', padx=15, pady=5)
        view_btn.pack(side='left', padx=(0, 10))
        
        # Signature status buttons
        status_frame = tk.Frame(buttons_frame, bg='white')
        status_frame.pack(side='right')
        
        signed_btn = tk.Button(status_frame, text="Mark as Signed", 
                              command=lambda: self.update_signature_status('Signed'),
                              bg='#27AE60', fg='white', font=('Segoe UI', 10, 'bold'),
                              relief='flat', padx=15, pady=5)
        signed_btn.pack(side='left', padx=(0, 5))
        
        cancelled_btn = tk.Button(status_frame, text="Mark as Cancelled", 
                                 command=lambda: self.update_signature_status('Cancelled'),
                                 bg='#E74C3C', fg='white', font=('Segoe UI', 10, 'bold'),
                                 relief='flat', padx=15, pady=5)
        cancelled_btn.pack(side='left')
        
        # Load contracts
        self.load_contracts()
        
        # Bind double-click to view
        self.contracts_tree.bind('<Double-1>', lambda e: self.view_contract())
    
    def load_contracts(self):
        """Load contracts into the tree"""
        try:
            # Clear existing items
            for item in self.contracts_tree.get_children():
                self.contracts_tree.delete(item)
            
            # Get filter
            status_filter = self.status_filter_var.get() if self.status_filter_var.get() != "All" else None
            
            # Load contracts
            contracts = self.contracts_manager.get_contracts(status_filter)
            
            for contract in contracts:
                contract_date = contract['contract_date'] if contract['contract_date'] else ""
                signed_date = contract['signed_date'] if contract['signed_date'] else ""
                total_formatted = f"${contract['total_amount']:,.2f}"
                
                self.contracts_tree.insert('', 'end', values=(
                    contract['id'],
                    contract['contract_number'],
                    contract['client_name'],
                    contract['phone'] or "",
                    total_formatted,
                    contract['signature_status'],
                    contract_date,
                    signed_date
                ))
        except Exception as e:
            print(f"Error loading contracts: {e}")
    
    def get_selected_contract_id(self):
        """Get selected contract ID"""
        selection = self.contracts_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a contract")
            return None
        
        item = self.contracts_tree.item(selection[0])
        return item['values'][0]
    
    def create_from_quotation(self):
        """Create contract from quotation dialog"""
        CreateContractDialog(self.parent, self.contracts_manager, self.load_contracts)
    
    def view_contract(self):
        """View contract details"""
        contract_id = self.get_selected_contract_id()
        if contract_id:
            contract = self.contracts_manager.get_contract_by_id(contract_id)
            if contract:
                ContractViewDialog(self.parent, contract)
    
    def update_signature_status(self, status):
        """Update contract signature status"""
        contract_id = self.get_selected_contract_id()
        if contract_id:
            if self.contracts_manager.update_contract_signature(contract_id, status):
                self.load_contracts()
                messagebox.showinfo("Success", f"Contract signature status updated to {status}")

class CreateContractDialog:
    """Dialog for creating contracts from quotations"""
    
    def __init__(self, parent, contracts_manager: ContractsManager, callback):
        self.parent = parent
        self.contracts_manager = contracts_manager
        self.callback = callback
        self.window = None
        
        self.create_dialog()
    
    def create_dialog(self):
        """Create contract creation dialog"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("Create Contract from Quotation")
        self.window.geometry("600x400")
        self.window.configure(bg='white')
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # Center the dialog
        self.window.update_idletasks()
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - (600 // 2)
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - (400 // 2)
        self.window.geometry(f"600x400+{x}+{y}")
        
        # Title
        title_label = tk.Label(self.window, text="Create Contract from Quotation", 
                              font=('Segoe UI', 16, 'bold'), bg='white', fg='#2C3E50')
        title_label.pack(pady=(20, 30))
        
        # Instructions
        instructions = tk.Label(self.window, 
                               text="Select an accepted quotation to create a contract:",
                               font=('Segoe UI', 10), bg='white', fg='#7F8C8D')
        instructions.pack(pady=(0, 20))
        
        # Quotations list
        list_frame = tk.Frame(self.window, bg='white', relief='solid', bd=1)
        list_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))
        
        # Treeview
        columns = ('ID', 'Client', 'Phone', 'City', 'Total', 'Created')
        self.quotations_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)
        
        # Configure columns
        for col in columns:
            self.quotations_tree.heading(col, text=col)
        
        # Column widths
        self.quotations_tree.column('ID', width=50)
        self.quotations_tree.column('Client', width=150)
        self.quotations_tree.column('Phone', width=120)
        self.quotations_tree.column('City', width=100)
        self.quotations_tree.column('Total', width=100)
        self.quotations_tree.column('Created', width=100)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.quotations_tree.yview)
        self.quotations_tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack
        self.quotations_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        scrollbar.pack(side='right', fill='y', pady=10)
        
        # Buttons
        buttons_frame = tk.Frame(self.window, bg='white')
        buttons_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        create_btn = tk.Button(buttons_frame, text="Create Contract", command=self.create_contract,
                              bg='#4A90E2', fg='white', font=('Segoe UI', 10, 'bold'),
                              relief='flat', padx=15, pady=5)
        create_btn.pack(side='left', padx=(0, 10))
        
        cancel_btn = tk.Button(buttons_frame, text="Cancel", command=self.window.destroy,
                              bg='#F8F9FA', fg='#2C3E50', font=('Segoe UI', 10, 'bold'),
                              relief='flat', padx=15, pady=5)
        cancel_btn.pack(side='left')
        
        # Load accepted quotations
        self.load_quotations()
    
    def load_quotations(self):
        """Load accepted quotations without contracts"""
        try:
            # Clear existing items
            for item in self.quotations_tree.get_children():
                self.quotations_tree.delete(item)
            
            # Load quotations
            quotations = self.contracts_manager.get_accepted_quotations_without_contracts()
            
            for quotation in quotations:
                created_date = quotation['created_at'][:10] if quotation['created_at'] else ""
                total_formatted = f"${quotation['total_amount']:,.2f}"
                
                self.quotations_tree.insert('', 'end', values=(
                    quotation['id'],
                    quotation['client_name'],
                    quotation['phone'] or "",
                    quotation['city'] or "",
                    total_formatted,
                    created_date
                ))
        except Exception as e:
            print(f"Error loading quotations: {e}")
    
    def create_contract(self):
        """Create contract from selected quotation"""
        selection = self.quotations_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a quotation")
            return
        
        item = self.quotations_tree.item(selection[0])
        quotation_id = item['values'][0]
        
        contract_id = self.contracts_manager.create_contract_from_quotation(quotation_id)
        if contract_id:
            self.callback()
            self.window.destroy()
            messagebox.showinfo("Success", f"Contract created successfully with ID: {contract_id}")

class ContractViewDialog:
    """Dialog for viewing contract details"""
    
    def __init__(self, parent, contract_data: dict):
        self.parent = parent
        self.contract_data = contract_data
        self.window = None
        
        self.create_dialog()
    
    def create_dialog(self):
        """Create contract view dialog"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(f"Contract Details - {self.contract_data['contract_number']}")
        self.window.geometry("700x600")
        self.window.configure(bg='white')
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # Center the dialog
        self.window.update_idletasks()
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - (700 // 2)
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - (600 // 2)
        self.window.geometry(f"700x600+{x}+{y}")
        
        # Create scrollable content
        main_canvas = tk.Canvas(self.window, bg='white')
        scrollbar = ttk.Scrollbar(self.window, orient="vertical", command=main_canvas.yview)
        scrollable_frame = tk.Frame(main_canvas, bg='white')
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: main_canvas.configure(scrollregion=main_canvas.bbox("all"))
        )
        
        main_canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        main_canvas.configure(yscrollcommand=scrollbar.set)
        
        # Title
        title_label = tk.Label(scrollable_frame, text=f"Contract {self.contract_data['contract_number']}", 
                              font=('Segoe UI', 18, 'bold'), bg='white', fg='#2C3E50')
        title_label.pack(pady=(20, 30))
        
        # Contract Information
        self.create_info_section(scrollable_frame, "Contract Information", [
            ("Contract Number", self.contract_data['contract_number']),
            ("Contract Date", self.contract_data['contract_date'] or "N/A"),
            ("Signature Status", self.contract_data['signature_status']),
            ("Signed Date", self.contract_data['signed_date'] or "N/A"),
            ("Created By", self.contract_data.get('created_by_name', 'N/A'))
        ])
        
        # Client Information
        self.create_info_section(scrollable_frame, "Client Information", [
            ("Client Name", self.contract_data['client_name']),
            ("Phone", self.contract_data['phone'] or "N/A"),
            ("Email", self.contract_data['email'] or "N/A"),
            ("City", self.contract_data['city'] or "N/A")
        ])
        
        # Project Details
        item_names = [item['item_name'] for item in self.contract_data.get('items', [])]
        items_text = ', '.join(item_names) if item_names else 'N/A'
        
        self.create_info_section(scrollable_frame, "Project Details", [
            ("Item Types", items_text),
            ("Area (sqm)", f"{self.contract_data['area_sqm']:.1f}"),
            ("Number of Devices", str(self.contract_data['num_devices'])),
            ("Price per sqm", f"${self.contract_data['price_per_sqm']:.2f}")
        ])
        
        # Pricing
        subtotal = self.contract_data['area_sqm'] * self.contract_data['price_per_sqm']
        discount_amount = subtotal * (self.contract_data['discount'] / 100)
        
        self.create_info_section(scrollable_frame, "Pricing", [
            ("Subtotal", f"${subtotal:.2f}"),
            ("Discount", f"{self.contract_data['discount']:.1f}% (-${discount_amount:.2f})"),
            ("Total Amount", f"${self.contract_data['total_amount']:,.2f}")
        ])
        
        # Close button
        close_btn = tk.Button(scrollable_frame, text="Close", command=self.window.destroy,
                             bg='#F8F9FA', fg='#2C3E50', font=('Segoe UI', 10, 'bold'),
                             relief='flat', padx=20, pady=8)
        close_btn.pack(pady=20)
        
        # Pack canvas and scrollbar
        main_canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def create_info_section(self, parent, title, items):
        """Create an information section"""
        # Section frame
        section_frame = tk.Frame(parent, bg='white', relief='solid', bd=1)
        section_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        # Section title
        title_label = tk.Label(section_frame, text=title, font=('Segoe UI', 12, 'bold'),
                              bg='white', fg='#2C3E50')
        title_label.pack(pady=(15, 10))
        
        # Items
        for label, value in items:
            item_frame = tk.Frame(section_frame, bg='white')
            item_frame.pack(fill='x', padx=20, pady=2)
            
            tk.Label(item_frame, text=f"{label}:", font=('Segoe UI', 10, 'bold'),
                    bg='white', fg='#2C3E50').pack(side='left')
            
            tk.Label(item_frame, text=str(value), font=('Segoe UI', 10),
                    bg='white', fg='#7F8C8D').pack(side='right')
        
        # Bottom padding
        tk.Frame(section_frame, bg='white', height=15).pack()
