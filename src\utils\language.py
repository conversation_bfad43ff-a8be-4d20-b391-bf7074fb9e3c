"""
Multi-language support for Beyond Smart Glass Tracker
"""

import json
import os

class LanguageManager:
    def __init__(self):
        self.current_language = 'ar'  # Default to Arabic
        self.translations = {}
        self.load_translations()
    
    def load_translations(self):
        """Load translation files"""
        self.translations = {
            'ar': {
                # Main Navigation
                'app_title': 'Beyond Smart Glass',
                'dashboard': 'لوحة التحكم',
                'quotations': 'العروض',
                'contracts': 'العقود',
                'manufacturing': 'التصنيع',
                'installation': 'التركيب',
                'inventory': 'المخزون',
                'admin': 'إدارة النظام',
                'settings': 'الإعدادات',
                'logout': 'تسجيل الخروج',
                
                # Login
                'login_title': 'تسجيل الدخول',
                'username': 'اسم المستخدم',
                'password': 'كلمة المرور',
                'login': 'دخول',
                'login_subtitle': 'يرجى تسجيل الدخول للمتابعة',
                
                # Quotations
                'quotations_management': 'إدارة العروض',
                'new_quotation': 'عرض جديد',
                'refresh': 'تحديث',
                'total_quotations': 'إجمالي العروض',
                'accepted_quotations': 'العروض المقبولة',
                'under_review': 'قيد المراجعة',
                'this_month': 'هذا الشهر',
                'quotations_list': 'قائمة العروض',
                'search': 'البحث...',
                
                # Quotation Form
                'add_new_quotation': 'إضافة عرض سعر جديد',
                'client_name': 'اسم العميل',
                'phone': 'رقم الهاتف',
                'city': 'المدينة',
                'project_type': 'نوع المشروع',
                'glass_type': 'نوع الزجاج',
                'area_sqm': 'المساحة (متر مربع)',
                'unit_price': 'سعر المتر (ريال)',
                'total_amount': 'المبلغ الإجمالي (ريال)',
                'notes': 'ملاحظات',
                'cancel': 'إلغاء',
                'save_quotation': 'حفظ العرض',
                
                # Table Headers
                'id': 'الرقم',
                'client': 'العميل',
                'phone_header': 'الهاتف',
                'city_header': 'المدينة',
                'total': 'المبلغ',
                'status': 'الحالة',
                'created': 'التاريخ',
                
                # Status
                'new': 'جديد',
                'accepted': 'مقبول',
                'rejected': 'مرفوض',
                'pending': 'قيد المراجعة',
                
                # Project Types
                'windows': 'نوافذ',
                'doors': 'أبواب',
                'facades': 'واجهات',
                'kitchens': 'مطابخ',
                'bathrooms': 'حمامات',
                'other': 'أخرى',
                
                # Glass Types
                'regular_glass': 'زجاج عادي',
                'tempered_glass': 'زجاج مقسى',
                'double_glass': 'زجاج مزدوج',
                'smart_glass': 'زجاج ذكي',
                'colored_glass': 'زجاج ملون',
                
                # Messages
                'success': 'نجح',
                'error': 'خطأ',
                'warning': 'تحذير',
                'info': 'معلومات',
                'quotation_saved': 'تم حفظ العرض بنجاح!',
                'data_updated': 'تم تحديث البيانات بنجاح!',
                'fill_required_fields': 'يرجى ملء جميع الحقول المطلوبة',
                
                # Settings
                'language_settings': 'إعدادات اللغة',
                'item_management': 'إدارة العناصر',
                'dropdown_management': 'إدارة القوائم المنسدلة',
                'add_item': 'إضافة عنصر',
                'edit_item': 'تعديل عنصر',
                'delete_item': 'حذف عنصر',
                'item_name': 'اسم العنصر',
                'item_category': 'فئة العنصر',
                'save': 'حفظ',
                'delete': 'حذف',
                'edit': 'تعديل',
                'view': 'عرض',
                'language': 'اللغة',
                'arabic': 'العربية',
                'english': 'English'
            },
            'en': {
                # Main Navigation
                'app_title': 'Beyond Smart Glass',
                'dashboard': 'Dashboard',
                'quotations': 'Quotations',
                'contracts': 'Contracts',
                'manufacturing': 'Manufacturing',
                'installation': 'Installation',
                'inventory': 'Inventory',
                'admin': 'Admin Panel',
                'settings': 'Settings',
                'logout': 'Logout',
                
                # Login
                'login_title': 'Login',
                'username': 'Username',
                'password': 'Password',
                'login': 'Login',
                'login_subtitle': 'Please login to continue',
                
                # Quotations
                'quotations_management': 'Quotations Management',
                'new_quotation': 'New Quotation',
                'refresh': 'Refresh',
                'total_quotations': 'Total Quotations',
                'accepted_quotations': 'Accepted Quotations',
                'under_review': 'Under Review',
                'this_month': 'This Month',
                'quotations_list': 'Quotations List',
                'search': 'Search...',
                
                # Quotation Form
                'add_new_quotation': 'Add New Quotation',
                'client_name': 'Client Name',
                'phone': 'Phone Number',
                'city': 'City',
                'project_type': 'Project Type',
                'glass_type': 'Glass Type',
                'area_sqm': 'Area (sqm)',
                'unit_price': 'Unit Price (SAR)',
                'total_amount': 'Total Amount (SAR)',
                'notes': 'Notes',
                'cancel': 'Cancel',
                'save_quotation': 'Save Quotation',
                
                # Table Headers
                'id': 'ID',
                'client': 'Client',
                'phone_header': 'Phone',
                'city_header': 'City',
                'total': 'Total',
                'status': 'Status',
                'created': 'Created',
                
                # Status
                'new': 'New',
                'accepted': 'Accepted',
                'rejected': 'Rejected',
                'pending': 'Pending',
                
                # Project Types
                'windows': 'Windows',
                'doors': 'Doors',
                'facades': 'Facades',
                'kitchens': 'Kitchens',
                'bathrooms': 'Bathrooms',
                'other': 'Other',
                
                # Glass Types
                'regular_glass': 'Regular Glass',
                'tempered_glass': 'Tempered Glass',
                'double_glass': 'Double Glass',
                'smart_glass': 'Smart Glass',
                'colored_glass': 'Colored Glass',
                
                # Messages
                'success': 'Success',
                'error': 'Error',
                'warning': 'Warning',
                'info': 'Info',
                'quotation_saved': 'Quotation saved successfully!',
                'data_updated': 'Data updated successfully!',
                'fill_required_fields': 'Please fill all required fields',
                
                # Settings
                'language_settings': 'Language Settings',
                'item_management': 'Item Management',
                'dropdown_management': 'Dropdown Management',
                'add_item': 'Add Item',
                'edit_item': 'Edit Item',
                'delete_item': 'Delete Item',
                'item_name': 'Item Name',
                'item_category': 'Item Category',
                'save': 'Save',
                'delete': 'Delete',
                'edit': 'Edit',
                'view': 'View',
                'language': 'Language',
                'arabic': 'العربية',
                'english': 'English'
            }
        }
    
    def set_language(self, language_code):
        """Set current language"""
        if language_code in self.translations:
            self.current_language = language_code
            return True
        return False
    
    def get_text(self, key):
        """Get translated text for current language"""
        return self.translations.get(self.current_language, {}).get(key, key)
    
    def get_current_language(self):
        """Get current language code"""
        return self.current_language
    
    def get_available_languages(self):
        """Get list of available languages"""
        return list(self.translations.keys())

# Global language manager instance
language_manager = LanguageManager()

def _(key):
    """Shorthand function for getting translated text"""
    return language_manager.get_text(key)
