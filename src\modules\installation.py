"""
Installation Scheduler Module
"""

import tkinter as tk
from tkinter import messagebox, ttk
from datetime import datetime, date
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from database.models import DatabaseManager
except ImportError:
    import sqlite3

class InstallationManager:
    """Handles installation operations"""
    
    def __init__(self, db: DatabaseManager):
        self.db = db
    
    def create_installation_record(self, contract_id: int, team_assigned: str, scheduled_date: str) -> int:
        """Create an installation record for a contract"""
        try:
            # Check if installation record already exists
            existing = self.db.execute_query(
                "SELECT id FROM installations WHERE contract_id = ?",
                (contract_id,)
            )
            
            if existing:
                messagebox.showerror("Error", "Installation record already exists for this contract")
                return None
            
            # Create installation record
            installation_id = self.db.get_last_insert_id(
                "INSERT INTO installations (contract_id, team_assigned, scheduled_date) VALUES (?, ?, ?)",
                (contract_id, team_assigned, scheduled_date)
            )
            
            return installation_id
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to create installation record: {str(e)}")
            return None
    
    def update_installation_status(self, installation_id: int, status: str, completion_date: str = None) -> bool:
        """Update installation status"""
        try:
            # Set completion date if marking as completed
            if status == 'Completed' and not completion_date:
                completion_date = datetime.now().date()
            
            self.db.execute_update(
                """UPDATE installations SET status = ?, completion_date = ?, 
                   updated_at = CURRENT_TIMESTAMP WHERE id = ?""",
                (status, completion_date, installation_id)
            )
            return True
        except Exception as e:
            messagebox.showerror("Error", f"Failed to update installation status: {str(e)}")
            return False
    
    def update_installation_details(self, installation_id: int, team_assigned: str, scheduled_date: str, notes: str) -> bool:
        """Update installation details"""
        try:
            self.db.execute_update(
                """UPDATE installations SET team_assigned = ?, scheduled_date = ?, notes = ?, 
                   updated_at = CURRENT_TIMESTAMP WHERE id = ?""",
                (team_assigned, scheduled_date, notes, installation_id)
            )
            return True
        except Exception as e:
            messagebox.showerror("Error", f"Failed to update installation details: {str(e)}")
            return False
    
    def get_installation_records(self, status_filter=None) -> list:
        """Get all installation records with optional status filter"""
        query = """
            SELECT i.*, c.contract_number, q.client_name, q.phone, q.city, q.area_sqm, q.total_amount
            FROM installations i
            JOIN contracts c ON i.contract_id = c.id
            JOIN quotations q ON c.quotation_id = q.id
        """
        params = ()
        
        if status_filter:
            query += " WHERE i.status = ?"
            params = (status_filter,)
        
        query += " ORDER BY i.scheduled_date ASC"
        
        return self.db.execute_query(query, params)
    
    def get_installation_by_id(self, installation_id: int) -> dict:
        """Get installation record by ID with full details"""
        records = self.db.execute_query(
            """SELECT i.*, c.contract_number, c.contract_date, c.signature_status,
                      q.client_name, q.phone, q.email, q.city, q.area_sqm, q.num_devices, 
                      q.total_amount, m.status as manufacturing_status
               FROM installations i
               JOIN contracts c ON i.contract_id = c.id
               JOIN quotations q ON c.quotation_id = q.id
               LEFT JOIN manufacturing m ON c.id = m.contract_id
               WHERE i.id = ?""",
            (installation_id,)
        )
        
        if not records:
            return None
        
        return records[0]
    
    def get_completed_manufacturing_without_installation(self) -> list:
        """Get completed manufacturing records that don't have installation scheduled yet"""
        return self.db.execute_query(
            """SELECT c.*, q.client_name, q.total_amount, m.completion_date as manufacturing_completed
               FROM contracts c
               JOIN quotations q ON c.quotation_id = q.id
               JOIN manufacturing m ON c.id = m.contract_id
               LEFT JOIN installations i ON c.id = i.contract_id
               WHERE m.status = 'Completed' AND i.id IS NULL
               ORDER BY m.completion_date DESC"""
        )

class InstallationModule:
    """Installation module UI"""
    
    def __init__(self, parent, db: DatabaseManager, current_user: dict):
        self.parent = parent
        self.db = db
        self.current_user = current_user
        self.installation_manager = InstallationManager(db)
        self.installation_tree = None
        self.status_filter_var = None
        
        self.create_interface()
    
    def create_interface(self):
        """Create installation interface"""
        # Header
        header_frame = tk.Frame(self.parent, bg='white')
        header_frame.pack(fill='x', padx=20, pady=(20, 10))
        
        title_label = tk.Label(header_frame, text="Installation Scheduler", 
                              font=('Segoe UI', 16, 'bold'), bg='white', fg='#2C3E50')
        title_label.pack(side='left')
        
        # Header buttons
        btn_frame = tk.Frame(header_frame, bg='white')
        btn_frame.pack(side='right')
        
        new_btn = tk.Button(btn_frame, text="Schedule Installation", 
                           command=self.schedule_installation,
                           bg='#4A90E2', fg='white', font=('Segoe UI', 10, 'bold'),
                           relief='flat', padx=15, pady=5)
        new_btn.pack(side='left', padx=(0, 10))
        
        refresh_btn = tk.Button(btn_frame, text="Refresh", command=self.load_installations,
                               bg='#F8F9FA', fg='#2C3E50', font=('Segoe UI', 10, 'bold'),
                               relief='flat', padx=15, pady=5)
        refresh_btn.pack(side='left')
        
        # Filters
        filter_frame = tk.Frame(self.parent, bg='white')
        filter_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        tk.Label(filter_frame, text="Status Filter:", font=('Segoe UI', 10), bg='white').pack(side='left', padx=(0, 10))
        
        self.status_filter_var = tk.StringVar(value="All")
        status_combo = ttk.Combobox(filter_frame, textvariable=self.status_filter_var,
                                   values=["All", "Scheduled", "In Progress", "Completed", "Cancelled"],
                                   state="readonly", width=15)
        status_combo.pack(side='left', padx=(0, 10))
        status_combo.bind('<<ComboboxSelected>>', lambda e: self.load_installations())
        
        # Installation list
        list_frame = tk.Frame(self.parent, bg='white', relief='solid', bd=1)
        list_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Treeview
        columns = ('ID', 'Contract #', 'Client', 'Team', 'Scheduled Date', 'Status', 'Completion Date', 'City')
        self.installation_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        for col in columns:
            self.installation_tree.heading(col, text=col)
        
        # Column widths
        self.installation_tree.column('ID', width=50)
        self.installation_tree.column('Contract #', width=120)
        self.installation_tree.column('Client', width=150)
        self.installation_tree.column('Team', width=120)
        self.installation_tree.column('Scheduled Date', width=100)
        self.installation_tree.column('Status', width=100)
        self.installation_tree.column('Completion Date', width=120)
        self.installation_tree.column('City', width=100)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.installation_tree.yview)
        self.installation_tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack
        self.installation_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        scrollbar.pack(side='right', fill='y', pady=10)
        
        # Buttons
        buttons_frame = tk.Frame(self.parent, bg='white')
        buttons_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        view_btn = tk.Button(buttons_frame, text="View Details", command=self.view_installation,
                            bg='#4A90E2', fg='white', font=('Segoe UI', 10, 'bold'),
                            relief='flat', padx=15, pady=5)
        view_btn.pack(side='left', padx=(0, 10))
        
        edit_btn = tk.Button(buttons_frame, text="Edit Details", command=self.edit_installation,
                            bg='#17A2B8', fg='white', font=('Segoe UI', 10, 'bold'),
                            relief='flat', padx=15, pady=5)
        edit_btn.pack(side='left', padx=(0, 10))
        
        # Status buttons
        status_frame = tk.Frame(buttons_frame, bg='white')
        status_frame.pack(side='right')
        
        start_btn = tk.Button(status_frame, text="Start Installation", 
                             command=lambda: self.update_status('In Progress'),
                             bg='#F39C12', fg='white', font=('Segoe UI', 10, 'bold'),
                             relief='flat', padx=15, pady=5)
        start_btn.pack(side='left', padx=(0, 5))
        
        complete_btn = tk.Button(status_frame, text="Mark Completed", 
                                command=lambda: self.update_status('Completed'),
                                bg='#27AE60', fg='white', font=('Segoe UI', 10, 'bold'),
                                relief='flat', padx=15, pady=5)
        complete_btn.pack(side='left', padx=(0, 5))
        
        cancel_btn = tk.Button(status_frame, text="Cancel Installation", 
                              command=lambda: self.update_status('Cancelled'),
                              bg='#E74C3C', fg='white', font=('Segoe UI', 10, 'bold'),
                              relief='flat', padx=15, pady=5)
        cancel_btn.pack(side='left')
        
        # Load installation records
        self.load_installations()
        
        # Bind double-click to view
        self.installation_tree.bind('<Double-1>', lambda e: self.view_installation())
    
    def load_installations(self):
        """Load installation records into the tree"""
        try:
            # Clear existing items
            for item in self.installation_tree.get_children():
                self.installation_tree.delete(item)
            
            # Get filter
            status_filter = self.status_filter_var.get() if self.status_filter_var.get() != "All" else None
            
            # Load installation records
            records = self.installation_manager.get_installation_records(status_filter)
            
            for record in records:
                scheduled_date = record['scheduled_date'] if record['scheduled_date'] else ""
                completion_date = record['completion_date'] if record['completion_date'] else ""
                
                self.installation_tree.insert('', 'end', values=(
                    record['id'],
                    record['contract_number'],
                    record['client_name'],
                    record['team_assigned'] or "Not assigned",
                    scheduled_date,
                    record['status'],
                    completion_date,
                    record['city'] or ""
                ))
        except Exception as e:
            print(f"Error loading installation records: {e}")
    
    def get_selected_installation_id(self):
        """Get selected installation record ID"""
        selection = self.installation_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select an installation record")
            return None
        
        item = self.installation_tree.item(selection[0])
        return item['values'][0]
    
    def schedule_installation(self):
        """Schedule installation for a contract"""
        ScheduleInstallationDialog(self.parent, self.installation_manager, self.load_installations)
    
    def view_installation(self):
        """View installation details"""
        installation_id = self.get_selected_installation_id()
        if installation_id:
            record = self.installation_manager.get_installation_by_id(installation_id)
            if record:
                InstallationViewDialog(self.parent, record)
    
    def edit_installation(self):
        """Edit installation details"""
        installation_id = self.get_selected_installation_id()
        if installation_id:
            record = self.installation_manager.get_installation_by_id(installation_id)
            if record:
                InstallationEditDialog(self.parent, self.installation_manager, record, self.load_installations)
    
    def update_status(self, status):
        """Update installation status"""
        installation_id = self.get_selected_installation_id()
        if installation_id:
            if self.installation_manager.update_installation_status(installation_id, status):
                self.load_installations()
                messagebox.showinfo("Success", f"Installation status updated to {status}")

class ScheduleInstallationDialog:
    """Dialog for scheduling installations"""

    def __init__(self, parent, installation_manager: InstallationManager, callback):
        self.parent = parent
        self.installation_manager = installation_manager
        self.callback = callback
        self.window = None

        self.create_dialog()

    def create_dialog(self):
        """Create schedule installation dialog"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("Schedule Installation")
        self.window.geometry("600x500")
        self.window.configure(bg='white')
        self.window.transient(self.parent)
        self.window.grab_set()

        # Center the dialog
        self.window.update_idletasks()
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - (600 // 2)
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - (500 // 2)
        self.window.geometry(f"600x500+{x}+{y}")

        # Title
        title_label = tk.Label(self.window, text="Schedule Installation",
                              font=('Segoe UI', 16, 'bold'), bg='white', fg='#2C3E50')
        title_label.pack(pady=(20, 20))

        # Instructions
        instructions = tk.Label(self.window,
                               text="Select a completed manufacturing job to schedule installation:",
                               font=('Segoe UI', 10), bg='white', fg='#7F8C8D')
        instructions.pack(pady=(0, 20))

        # Contracts list
        list_frame = tk.Frame(self.window, bg='white', relief='solid', bd=1)
        list_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

        # Treeview
        columns = ('ID', 'Contract #', 'Client', 'Total', 'Manufacturing Completed')
        self.contracts_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=8)

        # Configure columns
        for col in columns:
            self.contracts_tree.heading(col, text=col)

        # Column widths
        self.contracts_tree.column('ID', width=50)
        self.contracts_tree.column('Contract #', width=120)
        self.contracts_tree.column('Client', width=150)
        self.contracts_tree.column('Total', width=100)
        self.contracts_tree.column('Manufacturing Completed', width=150)

        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.contracts_tree.yview)
        self.contracts_tree.configure(yscrollcommand=scrollbar.set)

        # Pack
        self.contracts_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        scrollbar.pack(side='right', fill='y', pady=10)

        # Installation details
        details_frame = tk.Frame(self.window, bg='white', relief='solid', bd=1)
        details_frame.pack(fill='x', padx=20, pady=(0, 20))

        tk.Label(details_frame, text="Installation Details", font=('Segoe UI', 12, 'bold'),
                bg='white', fg='#2C3E50').pack(pady=(15, 10))

        # Team assignment
        team_frame = tk.Frame(details_frame, bg='white')
        team_frame.pack(fill='x', padx=20, pady=(0, 10))

        tk.Label(team_frame, text="Assigned Team:", font=('Segoe UI', 10), bg='white').pack(side='left')
        self.team_entry = tk.Entry(team_frame, width=30, font=('Segoe UI', 10))
        self.team_entry.pack(side='right')

        # Scheduled date
        date_frame = tk.Frame(details_frame, bg='white')
        date_frame.pack(fill='x', padx=20, pady=(0, 15))

        tk.Label(date_frame, text="Scheduled Date (YYYY-MM-DD):", font=('Segoe UI', 10), bg='white').pack(side='left')
        self.date_entry = tk.Entry(date_frame, width=20, font=('Segoe UI', 10))
        self.date_entry.pack(side='right')

        # Set default date to tomorrow
        tomorrow = datetime.now().date().replace(day=datetime.now().day + 1)
        self.date_entry.insert(0, tomorrow.strftime('%Y-%m-%d'))

        # Buttons
        buttons_frame = tk.Frame(self.window, bg='white')
        buttons_frame.pack(fill='x', padx=20, pady=(0, 20))

        schedule_btn = tk.Button(buttons_frame, text="Schedule Installation", command=self.schedule_installation,
                                bg='#4A90E2', fg='white', font=('Segoe UI', 10, 'bold'),
                                relief='flat', padx=15, pady=5)
        schedule_btn.pack(side='left', padx=(0, 10))

        cancel_btn = tk.Button(buttons_frame, text="Cancel", command=self.window.destroy,
                              bg='#F8F9FA', fg='#2C3E50', font=('Segoe UI', 10, 'bold'),
                              relief='flat', padx=15, pady=5)
        cancel_btn.pack(side='left')

        # Load completed manufacturing
        self.load_contracts()

    def load_contracts(self):
        """Load completed manufacturing without installation"""
        try:
            # Clear existing items
            for item in self.contracts_tree.get_children():
                self.contracts_tree.delete(item)

            # Load contracts
            contracts = self.installation_manager.get_completed_manufacturing_without_installation()

            for contract in contracts:
                manufacturing_date = contract['manufacturing_completed'] if contract['manufacturing_completed'] else ""
                total_formatted = f"${contract['total_amount']:,.2f}"

                self.contracts_tree.insert('', 'end', values=(
                    contract['id'],
                    contract['contract_number'],
                    contract['client_name'],
                    total_formatted,
                    manufacturing_date
                ))
        except Exception as e:
            print(f"Error loading contracts: {e}")

    def schedule_installation(self):
        """Schedule installation for selected contract"""
        selection = self.contracts_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a contract")
            return

        team = self.team_entry.get().strip()
        if not team:
            messagebox.showerror("Error", "Please assign a team")
            return

        scheduled_date = self.date_entry.get().strip()
        if not scheduled_date:
            messagebox.showerror("Error", "Please enter a scheduled date")
            return

        # Validate date format
        try:
            datetime.strptime(scheduled_date, '%Y-%m-%d')
        except ValueError:
            messagebox.showerror("Error", "Please enter date in YYYY-MM-DD format")
            return

        item = self.contracts_tree.item(selection[0])
        contract_id = item['values'][0]

        installation_id = self.installation_manager.create_installation_record(contract_id, team, scheduled_date)
        if installation_id:
            self.callback()
            self.window.destroy()
            messagebox.showinfo("Success", f"Installation scheduled successfully with ID: {installation_id}")
