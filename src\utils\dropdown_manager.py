"""
Dropdown and Items Management System
"""

import json
import os
from typing import Dict, List

class DropdownManager:
    def __init__(self):
        self.data_file = "data/dropdown_items.json"
        self.items = {}
        self.load_items()
    
    def load_items(self):
        """Load dropdown items from file"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    self.items = json.load(f)
            else:
                self.create_default_items()
        except Exception as e:
            print(f"Error loading dropdown items: {e}")
            self.create_default_items()
    
    def save_items(self):
        """Save dropdown items to file"""
        try:
            os.makedirs(os.path.dirname(self.data_file), exist_ok=True)
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.items, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"Error saving dropdown items: {e}")
            return False
    
    def create_default_items(self):
        """Create default dropdown items"""
        self.items = {
            'project_types': {
                'ar': ['نوافذ', 'أبواب', 'واجهات', 'مطابخ', 'حمامات', 'أخرى'],
                'en': ['Windows', 'Doors', 'Facades', 'Kitchens', 'Bathrooms', 'Other']
            },
            'glass_types': {
                'ar': ['زجاج عادي', 'زجاج مقسى', 'زجاج مزدوج', 'زجاج ذكي', 'زجاج ملون'],
                'en': ['Regular Glass', 'Tempered Glass', 'Double Glass', 'Smart Glass', 'Colored Glass']
            },
            'cities': {
                'ar': ['الرياض', 'جدة', 'الدمام', 'مكة', 'المدينة', 'الطائف', 'تبوك', 'أبها', 'جازان', 'نجران'],
                'en': ['Riyadh', 'Jeddah', 'Dammam', 'Mecca', 'Medina', 'Taif', 'Tabuk', 'Abha', 'Jazan', 'Najran']
            },
            'status_types': {
                'ar': ['جديد', 'قيد المراجعة', 'مقبول', 'مرفوض', 'ملغي'],
                'en': ['New', 'Under Review', 'Accepted', 'Rejected', 'Cancelled']
            },
            'units': {
                'ar': ['متر مربع', 'متر طولي', 'قطعة', 'طن', 'كيلو'],
                'en': ['Square Meter', 'Linear Meter', 'Piece', 'Ton', 'Kilogram']
            }
        }
        self.save_items()
    
    def get_items(self, category: str, language: str = 'ar') -> List[str]:
        """Get items for a specific category and language"""
        return self.items.get(category, {}).get(language, [])
    
    def add_item(self, category: str, item_ar: str, item_en: str) -> bool:
        """Add new item to category"""
        try:
            if category not in self.items:
                self.items[category] = {'ar': [], 'en': []}
            
            if item_ar not in self.items[category]['ar']:
                self.items[category]['ar'].append(item_ar)
            
            if item_en not in self.items[category]['en']:
                self.items[category]['en'].append(item_en)
            
            return self.save_items()
        except Exception as e:
            print(f"Error adding item: {e}")
            return False
    
    def remove_item(self, category: str, item_ar: str, item_en: str) -> bool:
        """Remove item from category"""
        try:
            if category in self.items:
                if item_ar in self.items[category]['ar']:
                    self.items[category]['ar'].remove(item_ar)
                if item_en in self.items[category]['en']:
                    self.items[category]['en'].remove(item_en)
                return self.save_items()
            return False
        except Exception as e:
            print(f"Error removing item: {e}")
            return False
    
    def update_item(self, category: str, old_item_ar: str, old_item_en: str, 
                   new_item_ar: str, new_item_en: str) -> bool:
        """Update existing item"""
        try:
            if self.remove_item(category, old_item_ar, old_item_en):
                return self.add_item(category, new_item_ar, new_item_en)
            return False
        except Exception as e:
            print(f"Error updating item: {e}")
            return False
    
    def get_categories(self) -> List[str]:
        """Get all available categories"""
        return list(self.items.keys())
    
    def add_category(self, category: str) -> bool:
        """Add new category"""
        try:
            if category not in self.items:
                self.items[category] = {'ar': [], 'en': []}
                return self.save_items()
            return False
        except Exception as e:
            print(f"Error adding category: {e}")
            return False
    
    def remove_category(self, category: str) -> bool:
        """Remove category"""
        try:
            if category in self.items:
                del self.items[category]
                return self.save_items()
            return False
        except Exception as e:
            print(f"Error removing category: {e}")
            return False

# Global dropdown manager instance
dropdown_manager = DropdownManager()
