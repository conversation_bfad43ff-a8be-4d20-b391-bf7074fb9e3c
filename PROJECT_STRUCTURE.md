# Beyond Smart Glass Tracker - Project Structure

## Overview
This document outlines the complete structure of the Beyond Smart Glass Tracker application.

## Directory Structure

```
Beyond Smart Glass/
├── main.py                    # Application entry point
├── requirements.txt           # Python dependencies
├── install.bat               # Windows installation script
├── run.bat                   # Windows run script
├── README.md                 # User documentation
├── PROJECT_STRUCTURE.md      # This file
│
├── src/                      # Source code directory
│   ├── __init__.py
│   ├── app.py               # Main application class
│   │
│   ├── database/            # Database management
│   │   ├── __init__.py
│   │   └── models.py        # Database models and operations
│   │
│   ├── modules/             # Application modules
│   │   ├── __init__.py
│   │   ├── auth.py          # Authentication and user management
│   │   ├── dashboard.py     # Sales dashboard
│   │   ├── quotations.py    # Quotations management
│   │   ├── contracts.py     # Contracts management
│   │   ├── manufacturing.py # Manufacturing tracker
│   │   ├── installation.py  # Installation scheduler
│   │   ├── inventory.py     # Inventory management
│   │   └── admin.py         # Admin panel
│   │
│   ├── ui/                  # User interface components
│   │   ├── __init__.py
│   │   ├── styles.py        # UI styling constants
│   │   └── base_widgets.py  # Custom UI components
│   │
│   ├── utils/               # Utility functions
│   │   ├── __init__.py
│   │   ├── pdf_export.py    # PDF generation utilities
│   │   └── data_management.py # CSV export/import and backup
│   │
│   └── assets/              # Application assets (icons, images)
│
└── beyond_smart_glass.db    # SQLite database (created on first run)
```

## Core Components

### 1. Main Application (`src/app.py`)
- **BeyondSmartGlassApp**: Main application class
- Handles window setup, navigation, and module loading
- Manages user authentication and session
- Provides menu bar with data management features

### 2. Database Layer (`src/database/models.py`)
- **DatabaseManager**: Central database management class
- Handles SQLite database operations
- Creates and manages all database tables
- Provides query execution methods

### 3. Application Modules (`src/modules/`)

#### Authentication (`auth.py`)
- **AuthManager**: User authentication and management
- **UserManagementWindow**: Admin interface for user management
- **UserEditDialog**: User creation/editing dialog
- Password hashing and validation
- Role-based access control

#### Dashboard (`dashboard.py`)
- **DashboardModule**: Main dashboard interface
- Business metrics and statistics
- Recent activity tracking
- Low stock alerts
- Visual progress indicators

#### Quotations (`quotations.py`)
- **QuotationsManager**: Quotation business logic
- **QuotationsModule**: Quotation management interface
- **QuotationEditDialog**: Create/edit quotations
- **QuotationViewDialog**: View quotation details
- Auto-calculation of totals and discounts
- PDF export functionality

#### Contracts (`contracts.py`)
- **ContractsManager**: Contract business logic
- **ContractsModule**: Contract management interface
- **CreateContractDialog**: Convert quotations to contracts
- **ContractViewDialog**: View contract details
- Signature status tracking

#### Manufacturing (`manufacturing.py`)
- **ManufacturingManager**: Manufacturing business logic
- **ManufacturingModule**: Manufacturing tracking interface
- **StartManufacturingDialog**: Start manufacturing from contracts
- **ManufacturingViewDialog**: View manufacturing details
- **ManufacturingNotesDialog**: Edit notes and cutting instructions
- Production status tracking

#### Installation (`installation.py`)
- **InstallationManager**: Installation business logic
- **InstallationModule**: Installation scheduling interface
- **ScheduleInstallationDialog**: Schedule installations
- **InstallationViewDialog**: View installation details
- **InstallationEditDialog**: Edit installation details
- Team assignment and date scheduling

#### Inventory (`inventory.py`)
- **InventoryManager**: Inventory business logic
- **InventoryModule**: Inventory management interface
- **InventoryItemDialog**: Add/edit inventory items
- **StockAdjustmentDialog**: Adjust stock levels
- **TransactionHistoryDialog**: View transaction history
- Low stock alerts and notifications

#### Admin Panel (`admin.py`)
- **AdminModule**: Administrative interface
- **ItemTypesManager**: Item type management
- **ItemTypeDialog**: Add/edit item types
- User management integration
- System information display

### 4. User Interface (`src/ui/`)

#### Styles (`styles.py`)
- Color palette definitions
- Font configurations
- Widget style templates
- Layout constants

#### Base Widgets (`base_widgets.py`)
- **AnimatedButton**: Button with hover animations
- **StyledEntry**: Consistent entry widget styling
- **StyledLabel**: Consistent label styling
- **StyledFrame**: Consistent frame styling
- **NavigationButton**: Special navigation menu buttons
- **SearchEntry**: Search input with placeholder
- **StatusBar**: Application status bar

### 5. Utilities (`src/utils/`)

#### PDF Export (`pdf_export.py`)
- **PDFExporter**: PDF generation utilities
- Quotation PDF export with company branding
- Contract PDF export (placeholder)
- Inventory report export (placeholder)
- Uses ReportLab library

#### Data Management (`data_management.py`)
- **DataManager**: Data export/import and backup utilities
- CSV export for all major data types
- CSV import functionality
- Database backup and restore
- Bulk data export

## Database Schema

### Core Tables
- **users**: User accounts and authentication
- **item_types**: Product/service categories
- **quotations**: Customer quotations
- **quotation_items**: Items included in quotations (many-to-many)
- **contracts**: Contract management
- **manufacturing**: Production tracking
- **installations**: Installation scheduling
- **inventory**: Stock management
- **inventory_transactions**: Stock transaction history

### Key Relationships
- Quotations → Contracts (one-to-one)
- Contracts → Manufacturing (one-to-one)
- Contracts → Installations (one-to-one)
- Quotations → Quotation Items (one-to-many)
- Item Types → Quotation Items (one-to-many)
- Inventory → Inventory Transactions (one-to-many)
- Users → Quotations (one-to-many)

## Features Implemented

### ✅ Core Functionality
- User authentication with role-based access
- Complete quotation management with PDF export
- Contract creation and tracking
- Manufacturing progress tracking
- Installation scheduling
- Inventory management with transaction history
- Sales dashboard with statistics
- Admin panel for system management

### ✅ Data Management
- CSV export for all major data types
- CSV import for quotations
- Database backup and restore
- Bulk data export functionality

### ✅ User Interface
- Modern flat design with consistent styling
- Hover animations and smooth transitions
- Responsive layout with proper navigation
- Context menus and keyboard shortcuts
- Status bar with user information

### ✅ Business Logic
- Auto-calculation of quotation totals
- Status tracking across all modules
- Low stock alerts and notifications
- Role-based feature access
- Data validation and error handling

## Installation and Deployment

### Requirements
- Python 3.8 or higher
- Required packages (see requirements.txt)
- Windows operating system (optimized for)

### Installation Steps
1. Run `install.bat` to install dependencies
2. Run `run.bat` or `python main.py` to start application
3. Login with default credentials (admin/admin123)
4. Create additional users as needed

### Default Data
- Admin user account
- Sample item types
- Sample inventory items
- Empty database ready for use

## Security Considerations
- Password hashing using SHA-256
- Role-based access control
- Input validation and sanitization
- Database transaction safety
- Backup and restore functionality

## Future Enhancements
- Advanced reporting and analytics
- Email integration for quotations
- Barcode scanning for inventory
- Mobile app companion
- Cloud synchronization
- Advanced user permissions
- Audit trail and logging
- Integration with accounting systems
