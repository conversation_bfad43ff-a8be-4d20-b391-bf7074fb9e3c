"""
Base UI widgets with consistent styling and animations
"""

import tkinter as tk
from tkinter import ttk
from .styles import *

class AnimatedButton(tk.Button):
    """Button with hover animations"""
    
    def __init__(self, parent, style_type='primary', **kwargs):
        # Apply base style
        if style_type == 'primary':
            base_style = BUTTON_STYLE.copy()
        else:
            base_style = BUTTON_SECONDARY_STYLE.copy()
        
        # Merge with custom kwargs
        for key, value in kwargs.items():
            base_style[key] = value
        
        super().__init__(parent, **base_style)
        
        # Store original colors
        self.original_bg = base_style['bg']
        self.hover_bg = COLORS['primary_dark'] if style_type == 'primary' else COLORS['hover']
        
        # Bind hover events
        self.bind('<Enter>', self._on_enter)
        self.bind('<Leave>', self._on_leave)
        self.bind('<Button-1>', self._on_click)
        self.bind('<ButtonRelease-1>', self._on_release)
    
    def _on_enter(self, event):
        """Handle mouse enter"""
        self.config(bg=self.hover_bg)
    
    def _on_leave(self, event):
        """Handle mouse leave"""
        self.config(bg=self.original_bg)
    
    def _on_click(self, event):
        """Handle button click"""
        # Slightly darker on click
        darker_color = self._darken_color(self.hover_bg)
        self.config(bg=darker_color)
    
    def _on_release(self, event):
        """Handle button release"""
        self.config(bg=self.hover_bg)
    
    def _darken_color(self, color):
        """Darken a hex color"""
        if color.startswith('#'):
            color = color[1:]
        rgb = tuple(int(color[i:i+2], 16) for i in (0, 2, 4))
        darkened = tuple(max(0, c - 20) for c in rgb)
        return f"#{darkened[0]:02x}{darkened[1]:02x}{darkened[2]:02x}"

class StyledEntry(tk.Entry):
    """Entry widget with consistent styling"""
    
    def __init__(self, parent, **kwargs):
        style = ENTRY_STYLE.copy()
        style.update(kwargs)
        super().__init__(parent, **style)
        
        # Add focus events for better UX
        self.bind('<FocusIn>', self._on_focus_in)
        self.bind('<FocusOut>', self._on_focus_out)
    
    def _on_focus_in(self, event):
        """Handle focus in"""
        self.config(highlightbackground=COLORS['primary'])
    
    def _on_focus_out(self, event):
        """Handle focus out"""
        self.config(highlightbackground=COLORS['border'])

class StyledLabel(tk.Label):
    """Label with consistent styling"""
    
    def __init__(self, parent, style_type='default', **kwargs):
        style = LABEL_STYLE.copy()
        
        if style_type == 'heading':
            style['font'] = FONTS['heading']
        elif style_type == 'subheading':
            style['font'] = FONTS['subheading']
        elif style_type == 'small':
            style['font'] = FONTS['small']
        
        style.update(kwargs)
        super().__init__(parent, **style)

class StyledFrame(tk.Frame):
    """Frame with consistent styling"""
    
    def __init__(self, parent, style_type='default', **kwargs):
        if style_type == 'card':
            style = CARD_STYLE.copy()
        else:
            style = FRAME_STYLE.copy()
        
        style.update(kwargs)
        super().__init__(parent, **style)

class NavigationButton(AnimatedButton):
    """Special button for navigation menu"""
    
    def __init__(self, parent, text, command, icon=None, **kwargs):
        style = {
            'text': text,
            'command': command,
            'font': FONTS['button'],
            'bg': COLORS['surface'],
            'fg': COLORS['text_primary'],
            'relief': 'flat',
            'bd': 0,
            'padx': 20,
            'pady': 15,
            'anchor': 'w',
            'cursor': 'hand2'
        }
        style.update(kwargs)
        
        super().__init__(parent, style_type='secondary', **style)
        
        # Override hover colors for navigation
        self.original_bg = COLORS['surface']
        self.hover_bg = COLORS['hover']
        
        self.is_active = False
    
    def set_active(self, active=True):
        """Set button as active/inactive"""
        self.is_active = active
        if active:
            self.config(bg=COLORS['primary'], fg='white')
            self.original_bg = COLORS['primary']
            self.hover_bg = COLORS['primary_dark']
        else:
            self.config(bg=COLORS['surface'], fg=COLORS['text_primary'])
            self.original_bg = COLORS['surface']
            self.hover_bg = COLORS['hover']

class SearchEntry(StyledFrame):
    """Search entry with icon"""
    
    def __init__(self, parent, placeholder="Search...", **kwargs):
        super().__init__(parent, **kwargs)
        
        self.entry = StyledEntry(self, width=30)
        self.entry.pack(side='left', padx=(10, 5), pady=5)
        
        # Placeholder functionality
        self.placeholder = placeholder
        self.entry.insert(0, placeholder)
        self.entry.config(fg=COLORS['text_secondary'])
        
        self.entry.bind('<FocusIn>', self._on_focus_in)
        self.entry.bind('<FocusOut>', self._on_focus_out)
    
    def _on_focus_in(self, event):
        """Handle focus in"""
        if self.entry.get() == self.placeholder:
            self.entry.delete(0, tk.END)
            self.entry.config(fg=COLORS['text_primary'])
    
    def _on_focus_out(self, event):
        """Handle focus out"""
        if not self.entry.get():
            self.entry.insert(0, self.placeholder)
            self.entry.config(fg=COLORS['text_secondary'])
    
    def get(self):
        """Get entry value"""
        value = self.entry.get()
        return value if value != self.placeholder else ""
    
    def set(self, value):
        """Set entry value"""
        self.entry.delete(0, tk.END)
        if value:
            self.entry.insert(0, value)
            self.entry.config(fg=COLORS['text_primary'])
        else:
            self.entry.insert(0, self.placeholder)
            self.entry.config(fg=COLORS['text_secondary'])

class StatusBar(StyledFrame):
    """Status bar for the bottom of the application"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, bg=COLORS['surface'], **kwargs)
        
        self.status_label = StyledLabel(self, text="Ready", style_type='small')
        self.status_label.pack(side='left', padx=PADDING['medium'], pady=PADDING['small'])
        
        self.user_label = StyledLabel(self, text="", style_type='small')
        self.user_label.pack(side='right', padx=PADDING['medium'], pady=PADDING['small'])
    
    def set_status(self, message):
        """Set status message"""
        self.status_label.config(text=message)
    
    def set_user(self, username, role):
        """Set user information"""
        self.user_label.config(text=f"Logged in as: {username} ({role})")
