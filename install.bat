@echo off
echo ========================================
echo Beyond Smart Glass Tracker Installer
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo.
    echo Please install Python 3.8 or higher from:
    echo https://www.python.org/downloads/
    echo.
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

echo Python found. Checking version...
python --version

echo.
echo Installing required dependencies...
echo.

REM Install required packages
pip install customtkinter Pillow reportlab pandas matplotlib tkcalendar

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Failed to install some dependencies
    echo Please check your internet connection and try again
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Installation completed successfully!
echo ========================================
echo.
echo To run the application:
echo 1. Double-click on "run.bat" or
echo 2. Open Command Prompt in this folder and type: python main.py
echo.
echo Default login credentials:
echo Username: admin
echo Password: admin123
echo.
echo For help and documentation, see README.md
echo.
pause
