"""
Data Management Utilities - CSV Export/Import and Backup
"""

import csv
import os
import shutil
from datetime import datetime
from tkinter import filedialog, messagebox
import sys

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from database.models import DatabaseManager
except ImportError:
    import sqlite3

class DataManager:
    """Handles data export, import, and backup operations"""
    
    def __init__(self, db: DatabaseManager):
        self.db = db
    
    def export_quotations_to_csv(self, filename: str = None) -> bool:
        """Export quotations to CSV file"""
        try:
            if not filename:
                filename = filedialog.asksaveasfilename(
                    defaultextension=".csv",
                    filetypes=[("CSV files", "*.csv")],
                    title="Export Quotations to CSV",
                    initialname=f"quotations_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                )
            
            if not filename:
                return False
            
            # Get quotations data
            quotations = self.db.execute_query("""
                SELECT q.id, q.client_name, q.phone, q.email, q.city, q.area_sqm, 
                       q.num_devices, q.price_per_sqm, q.discount, q.total_amount, 
                       q.status, q.created_at, u.full_name as created_by
                FROM quotations q
                LEFT JOIN users u ON q.created_by = u.id
                ORDER BY q.created_at DESC
            """)
            
            # Write to CSV
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['ID', 'Client Name', 'Phone', 'Email', 'City', 'Area (sqm)', 
                             'Devices', 'Price per sqm', 'Discount (%)', 'Total Amount', 
                             'Status', 'Created Date', 'Created By']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for quotation in quotations:
                    writer.writerow({
                        'ID': quotation['id'],
                        'Client Name': quotation['client_name'],
                        'Phone': quotation['phone'] or '',
                        'Email': quotation['email'] or '',
                        'City': quotation['city'] or '',
                        'Area (sqm)': quotation['area_sqm'],
                        'Devices': quotation['num_devices'],
                        'Price per sqm': quotation['price_per_sqm'],
                        'Discount (%)': quotation['discount'],
                        'Total Amount': quotation['total_amount'],
                        'Status': quotation['status'],
                        'Created Date': quotation['created_at'][:10] if quotation['created_at'] else '',
                        'Created By': quotation['created_by'] or ''
                    })
            
            messagebox.showinfo("Success", f"Quotations exported successfully to:\n{filename}")
            return True
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export quotations: {str(e)}")
            return False
    
    def export_contracts_to_csv(self, filename: str = None) -> bool:
        """Export contracts to CSV file"""
        try:
            if not filename:
                filename = filedialog.asksaveasfilename(
                    defaultextension=".csv",
                    filetypes=[("CSV files", "*.csv")],
                    title="Export Contracts to CSV",
                    initialname=f"contracts_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                )
            
            if not filename:
                return False
            
            # Get contracts data
            contracts = self.db.execute_query("""
                SELECT c.id, c.contract_number, c.signature_status, c.signed_date, 
                       c.contract_date, q.client_name, q.phone, q.email, q.city, 
                       q.total_amount, c.created_at
                FROM contracts c
                JOIN quotations q ON c.quotation_id = q.id
                ORDER BY c.created_at DESC
            """)
            
            # Write to CSV
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['ID', 'Contract Number', 'Client Name', 'Phone', 'Email', 'City',
                             'Total Amount', 'Signature Status', 'Contract Date', 'Signed Date', 'Created Date']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for contract in contracts:
                    writer.writerow({
                        'ID': contract['id'],
                        'Contract Number': contract['contract_number'],
                        'Client Name': contract['client_name'],
                        'Phone': contract['phone'] or '',
                        'Email': contract['email'] or '',
                        'City': contract['city'] or '',
                        'Total Amount': contract['total_amount'],
                        'Signature Status': contract['signature_status'],
                        'Contract Date': contract['contract_date'] or '',
                        'Signed Date': contract['signed_date'] or '',
                        'Created Date': contract['created_at'][:10] if contract['created_at'] else ''
                    })
            
            messagebox.showinfo("Success", f"Contracts exported successfully to:\n{filename}")
            return True
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export contracts: {str(e)}")
            return False
    
    def export_inventory_to_csv(self, filename: str = None) -> bool:
        """Export inventory to CSV file"""
        try:
            if not filename:
                filename = filedialog.asksaveasfilename(
                    defaultextension=".csv",
                    filetypes=[("CSV files", "*.csv")],
                    title="Export Inventory to CSV",
                    initialname=f"inventory_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                )
            
            if not filename:
                return False
            
            # Get inventory data
            inventory = self.db.execute_query("""
                SELECT id, item_name, item_type, current_stock, min_stock_alert, 
                       unit, created_at, updated_at
                FROM inventory
                ORDER BY item_name
            """)
            
            # Write to CSV
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['ID', 'Item Name', 'Item Type', 'Current Stock', 'Min Stock Alert',
                             'Unit', 'Created Date', 'Updated Date']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for item in inventory:
                    writer.writerow({
                        'ID': item['id'],
                        'Item Name': item['item_name'],
                        'Item Type': item['item_type'],
                        'Current Stock': item['current_stock'],
                        'Min Stock Alert': item['min_stock_alert'],
                        'Unit': item['unit'],
                        'Created Date': item['created_at'][:10] if item['created_at'] else '',
                        'Updated Date': item['updated_at'][:10] if item['updated_at'] else ''
                    })
            
            messagebox.showinfo("Success", f"Inventory exported successfully to:\n{filename}")
            return True
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export inventory: {str(e)}")
            return False
    
    def export_all_data(self) -> bool:
        """Export all data to separate CSV files in a folder"""
        try:
            # Ask user for folder
            folder = filedialog.askdirectory(title="Select folder for data export")
            if not folder:
                return False
            
            # Create timestamped subfolder
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            export_folder = os.path.join(folder, f"beyond_smart_glass_export_{timestamp}")
            os.makedirs(export_folder, exist_ok=True)
            
            # Export each data type
            success_count = 0
            
            if self.export_quotations_to_csv(os.path.join(export_folder, "quotations.csv")):
                success_count += 1
            
            if self.export_contracts_to_csv(os.path.join(export_folder, "contracts.csv")):
                success_count += 1
            
            if self.export_inventory_to_csv(os.path.join(export_folder, "inventory.csv")):
                success_count += 1
            
            # Export additional tables
            self._export_table_to_csv("users", os.path.join(export_folder, "users.csv"))
            self._export_table_to_csv("item_types", os.path.join(export_folder, "item_types.csv"))
            self._export_table_to_csv("manufacturing", os.path.join(export_folder, "manufacturing.csv"))
            self._export_table_to_csv("installations", os.path.join(export_folder, "installations.csv"))
            
            messagebox.showinfo("Success", f"Data exported successfully to:\n{export_folder}")
            return True
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export all data: {str(e)}")
            return False
    
    def _export_table_to_csv(self, table_name: str, filename: str) -> bool:
        """Export a database table to CSV"""
        try:
            # Get table data
            data = self.db.execute_query(f"SELECT * FROM {table_name}")
            
            if not data:
                return True  # Empty table is not an error
            
            # Write to CSV
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = list(data[0].keys())
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for row in data:
                    writer.writerow(row)
            
            return True
            
        except Exception as e:
            print(f"Error exporting table {table_name}: {e}")
            return False
    
    def backup_database(self, filename: str = None) -> bool:
        """Create a backup of the database file"""
        try:
            if not filename:
                filename = filedialog.asksaveasfilename(
                    defaultextension=".db",
                    filetypes=[("Database files", "*.db"), ("All files", "*.*")],
                    title="Save Database Backup",
                    initialname=f"beyond_smart_glass_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
                )
            
            if not filename:
                return False
            
            # Copy database file
            shutil.copy2(self.db.db_path, filename)
            
            messagebox.showinfo("Success", f"Database backup created successfully:\n{filename}")
            return True
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to create database backup: {str(e)}")
            return False
    
    def restore_database(self, filename: str = None) -> bool:
        """Restore database from backup file"""
        try:
            if not filename:
                filename = filedialog.askopenfilename(
                    filetypes=[("Database files", "*.db"), ("All files", "*.*")],
                    title="Select Database Backup to Restore"
                )
            
            if not filename:
                return False
            
            # Confirm restore
            if not messagebox.askyesno("Confirm Restore", 
                                     "This will replace the current database with the backup.\n"
                                     "Are you sure you want to continue?"):
                return False
            
            # Create backup of current database first
            current_backup = f"{self.db.db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copy2(self.db.db_path, current_backup)
            
            # Restore from backup
            shutil.copy2(filename, self.db.db_path)
            
            messagebox.showinfo("Success", 
                               f"Database restored successfully from:\n{filename}\n\n"
                               f"Previous database backed up to:\n{current_backup}")
            return True
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to restore database: {str(e)}")
            return False
    
    def import_quotations_from_csv(self, filename: str = None) -> bool:
        """Import quotations from CSV file"""
        try:
            if not filename:
                filename = filedialog.askopenfilename(
                    filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                    title="Select CSV file to import quotations"
                )
            
            if not filename:
                return False
            
            # Read CSV file
            imported_count = 0
            with open(filename, 'r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                
                for row in reader:
                    try:
                        # Insert quotation (simplified - you may want to add validation)
                        self.db.execute_update("""
                            INSERT INTO quotations 
                            (client_name, phone, email, city, area_sqm, num_devices, 
                             price_per_sqm, discount, total_amount, status)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            row.get('Client Name', ''),
                            row.get('Phone', ''),
                            row.get('Email', ''),
                            row.get('City', ''),
                            float(row.get('Area (sqm)', 0)),
                            int(row.get('Devices', 0)),
                            float(row.get('Price per sqm', 0)),
                            float(row.get('Discount (%)', 0)),
                            float(row.get('Total Amount', 0)),
                            row.get('Status', 'Draft')
                        ))
                        imported_count += 1
                    except Exception as e:
                        print(f"Error importing row: {e}")
                        continue
            
            messagebox.showinfo("Success", f"Imported {imported_count} quotations successfully")
            return True
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to import quotations: {str(e)}")
            return False
