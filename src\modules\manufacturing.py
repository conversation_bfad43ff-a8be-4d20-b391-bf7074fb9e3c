"""
Manufacturing Tracker Module
"""

import tkinter as tk
from tkinter import messagebox, ttk
from datetime import datetime
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from database.models import DatabaseManager
except ImportError:
    import sqlite3

class ManufacturingManager:
    """Handles manufacturing operations"""
    
    def __init__(self, db: DatabaseManager):
        self.db = db
    
    def create_manufacturing_record(self, contract_id: int) -> int:
        """Create a manufacturing record for a contract"""
        try:
            # Check if manufacturing record already exists
            existing = self.db.execute_query(
                "SELECT id FROM manufacturing WHERE contract_id = ?",
                (contract_id,)
            )
            
            if existing:
                messagebox.showerror("Error", "Manufacturing record already exists for this contract")
                return None
            
            # Create manufacturing record
            manufacturing_id = self.db.get_last_insert_id(
                "INSERT INTO manufacturing (contract_id) VALUES (?)",
                (contract_id,)
            )
            
            return manufacturing_id
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to create manufacturing record: {str(e)}")
            return None
    
    def update_manufacturing_status(self, manufacturing_id: int, status: str, start_date: str = None, completion_date: str = None) -> bool:
        """Update manufacturing status"""
        try:
            # Set dates based on status
            if status == 'In Progress' and not start_date:
                start_date = datetime.now().date()
            elif status == 'Completed' and not completion_date:
                completion_date = datetime.now().date()
            
            self.db.execute_update(
                """UPDATE manufacturing SET status = ?, start_date = ?, completion_date = ?, 
                   updated_at = CURRENT_TIMESTAMP WHERE id = ?""",
                (status, start_date, completion_date, manufacturing_id)
            )
            return True
        except Exception as e:
            messagebox.showerror("Error", f"Failed to update manufacturing status: {str(e)}")
            return False
    
    def update_manufacturing_notes(self, manufacturing_id: int, notes: str, cutting_instructions: str) -> bool:
        """Update manufacturing notes and cutting instructions"""
        try:
            self.db.execute_update(
                """UPDATE manufacturing SET notes = ?, cutting_instructions = ?, 
                   updated_at = CURRENT_TIMESTAMP WHERE id = ?""",
                (notes, cutting_instructions, manufacturing_id)
            )
            return True
        except Exception as e:
            messagebox.showerror("Error", f"Failed to update manufacturing notes: {str(e)}")
            return False
    
    def get_manufacturing_records(self, status_filter=None) -> list:
        """Get all manufacturing records with optional status filter"""
        query = """
            SELECT m.*, c.contract_number, q.client_name, q.area_sqm, q.num_devices, q.total_amount
            FROM manufacturing m
            JOIN contracts c ON m.contract_id = c.id
            JOIN quotations q ON c.quotation_id = q.id
        """
        params = ()
        
        if status_filter:
            query += " WHERE m.status = ?"
            params = (status_filter,)
        
        query += " ORDER BY m.created_at DESC"
        
        return self.db.execute_query(query, params)
    
    def get_manufacturing_by_id(self, manufacturing_id: int) -> dict:
        """Get manufacturing record by ID with full details"""
        records = self.db.execute_query(
            """SELECT m.*, c.contract_number, c.contract_date, c.signature_status,
                      q.client_name, q.phone, q.email, q.city, q.area_sqm, q.num_devices, 
                      q.price_per_sqm, q.discount, q.total_amount
               FROM manufacturing m
               JOIN contracts c ON m.contract_id = c.id
               JOIN quotations q ON c.quotation_id = q.id
               WHERE m.id = ?""",
            (manufacturing_id,)
        )
        
        if not records:
            return None
        
        record = records[0]
        
        # Get quotation items
        items = self.db.execute_query(
            """SELECT qi.*, it.name as item_name
               FROM quotation_items qi
               JOIN item_types it ON qi.item_type_id = it.id
               WHERE qi.quotation_id = ?""",
            (record['quotation_id'] if 'quotation_id' in record else None,)
        )
        
        record['items'] = items
        return record
    
    def get_signed_contracts_without_manufacturing(self) -> list:
        """Get signed contracts that don't have manufacturing records yet"""
        return self.db.execute_query(
            """SELECT c.*, q.client_name, q.total_amount FROM contracts c
               JOIN quotations q ON c.quotation_id = q.id
               LEFT JOIN manufacturing m ON c.id = m.contract_id
               WHERE c.signature_status = 'Signed' AND m.id IS NULL
               ORDER BY c.contract_date DESC"""
        )

class ManufacturingModule:
    """Manufacturing module UI"""
    
    def __init__(self, parent, db: DatabaseManager, current_user: dict):
        self.parent = parent
        self.db = db
        self.current_user = current_user
        self.manufacturing_manager = ManufacturingManager(db)
        self.manufacturing_tree = None
        self.status_filter_var = None
        
        self.create_interface()
    
    def create_interface(self):
        """Create manufacturing interface"""
        # Header
        header_frame = tk.Frame(self.parent, bg='white')
        header_frame.pack(fill='x', padx=20, pady=(20, 10))
        
        title_label = tk.Label(header_frame, text="Manufacturing Tracker", 
                              font=('Segoe UI', 16, 'bold'), bg='white', fg='#2C3E50')
        title_label.pack(side='left')
        
        # Header buttons
        btn_frame = tk.Frame(header_frame, bg='white')
        btn_frame.pack(side='right')
        
        new_btn = tk.Button(btn_frame, text="Start Manufacturing", 
                           command=self.start_manufacturing,
                           bg='#4A90E2', fg='white', font=('Segoe UI', 10, 'bold'),
                           relief='flat', padx=15, pady=5)
        new_btn.pack(side='left', padx=(0, 10))
        
        refresh_btn = tk.Button(btn_frame, text="Refresh", command=self.load_manufacturing,
                               bg='#F8F9FA', fg='#2C3E50', font=('Segoe UI', 10, 'bold'),
                               relief='flat', padx=15, pady=5)
        refresh_btn.pack(side='left')
        
        # Filters
        filter_frame = tk.Frame(self.parent, bg='white')
        filter_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        tk.Label(filter_frame, text="Status Filter:", font=('Segoe UI', 10), bg='white').pack(side='left', padx=(0, 10))
        
        self.status_filter_var = tk.StringVar(value="All")
        status_combo = ttk.Combobox(filter_frame, textvariable=self.status_filter_var,
                                   values=["All", "Pending", "In Progress", "Completed"],
                                   state="readonly", width=15)
        status_combo.pack(side='left', padx=(0, 10))
        status_combo.bind('<<ComboboxSelected>>', lambda e: self.load_manufacturing())
        
        # Manufacturing list
        list_frame = tk.Frame(self.parent, bg='white', relief='solid', bd=1)
        list_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Treeview
        columns = ('ID', 'Contract #', 'Client', 'Area (sqm)', 'Devices', 'Status', 'Start Date', 'Completion Date')
        self.manufacturing_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        for col in columns:
            self.manufacturing_tree.heading(col, text=col)
        
        # Column widths
        self.manufacturing_tree.column('ID', width=50)
        self.manufacturing_tree.column('Contract #', width=120)
        self.manufacturing_tree.column('Client', width=150)
        self.manufacturing_tree.column('Area (sqm)', width=80)
        self.manufacturing_tree.column('Devices', width=70)
        self.manufacturing_tree.column('Status', width=100)
        self.manufacturing_tree.column('Start Date', width=100)
        self.manufacturing_tree.column('Completion Date', width=120)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.manufacturing_tree.yview)
        self.manufacturing_tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack
        self.manufacturing_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        scrollbar.pack(side='right', fill='y', pady=10)
        
        # Buttons
        buttons_frame = tk.Frame(self.parent, bg='white')
        buttons_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        view_btn = tk.Button(buttons_frame, text="View Details", command=self.view_manufacturing,
                            bg='#4A90E2', fg='white', font=('Segoe UI', 10, 'bold'),
                            relief='flat', padx=15, pady=5)
        view_btn.pack(side='left', padx=(0, 10))
        
        edit_btn = tk.Button(buttons_frame, text="Edit Notes", command=self.edit_notes,
                            bg='#17A2B8', fg='white', font=('Segoe UI', 10, 'bold'),
                            relief='flat', padx=15, pady=5)
        edit_btn.pack(side='left', padx=(0, 10))
        
        # Status buttons
        status_frame = tk.Frame(buttons_frame, bg='white')
        status_frame.pack(side='right')
        
        start_btn = tk.Button(status_frame, text="Start Production", 
                             command=lambda: self.update_status('In Progress'),
                             bg='#F39C12', fg='white', font=('Segoe UI', 10, 'bold'),
                             relief='flat', padx=15, pady=5)
        start_btn.pack(side='left', padx=(0, 5))
        
        complete_btn = tk.Button(status_frame, text="Mark Completed", 
                                command=lambda: self.update_status('Completed'),
                                bg='#27AE60', fg='white', font=('Segoe UI', 10, 'bold'),
                                relief='flat', padx=15, pady=5)
        complete_btn.pack(side='left')
        
        # Load manufacturing records
        self.load_manufacturing()
        
        # Bind double-click to view
        self.manufacturing_tree.bind('<Double-1>', lambda e: self.view_manufacturing())
    
    def load_manufacturing(self):
        """Load manufacturing records into the tree"""
        try:
            # Clear existing items
            for item in self.manufacturing_tree.get_children():
                self.manufacturing_tree.delete(item)
            
            # Get filter
            status_filter = self.status_filter_var.get() if self.status_filter_var.get() != "All" else None
            
            # Load manufacturing records
            records = self.manufacturing_manager.get_manufacturing_records(status_filter)
            
            for record in records:
                start_date = record['start_date'] if record['start_date'] else ""
                completion_date = record['completion_date'] if record['completion_date'] else ""
                
                self.manufacturing_tree.insert('', 'end', values=(
                    record['id'],
                    record['contract_number'],
                    record['client_name'],
                    f"{record['area_sqm']:.1f}",
                    record['num_devices'],
                    record['status'],
                    start_date,
                    completion_date
                ))
        except Exception as e:
            print(f"Error loading manufacturing records: {e}")
    
    def get_selected_manufacturing_id(self):
        """Get selected manufacturing record ID"""
        selection = self.manufacturing_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a manufacturing record")
            return None
        
        item = self.manufacturing_tree.item(selection[0])
        return item['values'][0]
    
    def start_manufacturing(self):
        """Start manufacturing for a contract"""
        StartManufacturingDialog(self.parent, self.manufacturing_manager, self.load_manufacturing)
    
    def view_manufacturing(self):
        """View manufacturing details"""
        manufacturing_id = self.get_selected_manufacturing_id()
        if manufacturing_id:
            record = self.manufacturing_manager.get_manufacturing_by_id(manufacturing_id)
            if record:
                ManufacturingViewDialog(self.parent, record)
    
    def edit_notes(self):
        """Edit manufacturing notes"""
        manufacturing_id = self.get_selected_manufacturing_id()
        if manufacturing_id:
            record = self.manufacturing_manager.get_manufacturing_by_id(manufacturing_id)
            if record:
                ManufacturingNotesDialog(self.parent, self.manufacturing_manager, record, self.load_manufacturing)
    
    def update_status(self, status):
        """Update manufacturing status"""
        manufacturing_id = self.get_selected_manufacturing_id()
        if manufacturing_id:
            if self.manufacturing_manager.update_manufacturing_status(manufacturing_id, status):
                self.load_manufacturing()
                messagebox.showinfo("Success", f"Manufacturing status updated to {status}")

class StartManufacturingDialog:
    """Dialog for starting manufacturing from contracts"""
    
    def __init__(self, parent, manufacturing_manager: ManufacturingManager, callback):
        self.parent = parent
        self.manufacturing_manager = manufacturing_manager
        self.callback = callback
        self.window = None
        
        self.create_dialog()
    
    def create_dialog(self):
        """Create start manufacturing dialog"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("Start Manufacturing")
        self.window.geometry("600x400")
        self.window.configure(bg='white')
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # Center the dialog
        self.window.update_idletasks()
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - (600 // 2)
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - (400 // 2)
        self.window.geometry(f"600x400+{x}+{y}")
        
        # Title
        title_label = tk.Label(self.window, text="Start Manufacturing", 
                              font=('Segoe UI', 16, 'bold'), bg='white', fg='#2C3E50')
        title_label.pack(pady=(20, 30))
        
        # Instructions
        instructions = tk.Label(self.window, 
                               text="Select a signed contract to start manufacturing:",
                               font=('Segoe UI', 10), bg='white', fg='#7F8C8D')
        instructions.pack(pady=(0, 20))
        
        # Contracts list
        list_frame = tk.Frame(self.window, bg='white', relief='solid', bd=1)
        list_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))
        
        # Treeview
        columns = ('ID', 'Contract #', 'Client', 'Total', 'Contract Date')
        self.contracts_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)
        
        # Configure columns
        for col in columns:
            self.contracts_tree.heading(col, text=col)
        
        # Column widths
        self.contracts_tree.column('ID', width=50)
        self.contracts_tree.column('Contract #', width=120)
        self.contracts_tree.column('Client', width=150)
        self.contracts_tree.column('Total', width=100)
        self.contracts_tree.column('Contract Date', width=100)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.contracts_tree.yview)
        self.contracts_tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack
        self.contracts_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        scrollbar.pack(side='right', fill='y', pady=10)
        
        # Buttons
        buttons_frame = tk.Frame(self.window, bg='white')
        buttons_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        start_btn = tk.Button(buttons_frame, text="Start Manufacturing", command=self.start_manufacturing,
                             bg='#4A90E2', fg='white', font=('Segoe UI', 10, 'bold'),
                             relief='flat', padx=15, pady=5)
        start_btn.pack(side='left', padx=(0, 10))
        
        cancel_btn = tk.Button(buttons_frame, text="Cancel", command=self.window.destroy,
                              bg='#F8F9FA', fg='#2C3E50', font=('Segoe UI', 10, 'bold'),
                              relief='flat', padx=15, pady=5)
        cancel_btn.pack(side='left')
        
        # Load signed contracts
        self.load_contracts()
    
    def load_contracts(self):
        """Load signed contracts without manufacturing"""
        try:
            # Clear existing items
            for item in self.contracts_tree.get_children():
                self.contracts_tree.delete(item)
            
            # Load contracts
            contracts = self.manufacturing_manager.get_signed_contracts_without_manufacturing()
            
            for contract in contracts:
                contract_date = contract['contract_date'] if contract['contract_date'] else ""
                total_formatted = f"${contract['total_amount']:,.2f}"
                
                self.contracts_tree.insert('', 'end', values=(
                    contract['id'],
                    contract['contract_number'],
                    contract['client_name'],
                    total_formatted,
                    contract_date
                ))
        except Exception as e:
            print(f"Error loading contracts: {e}")
    
    def start_manufacturing(self):
        """Start manufacturing for selected contract"""
        selection = self.contracts_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a contract")
            return
        
        item = self.contracts_tree.item(selection[0])
        contract_id = item['values'][0]
        
        manufacturing_id = self.manufacturing_manager.create_manufacturing_record(contract_id)
        if manufacturing_id:
            self.callback()
            self.window.destroy()
            messagebox.showinfo("Success", f"Manufacturing started successfully with ID: {manufacturing_id}")

class ManufacturingViewDialog:
    """Dialog for viewing manufacturing details"""

    def __init__(self, parent, manufacturing_data: dict):
        self.parent = parent
        self.manufacturing_data = manufacturing_data
        self.window = None

        self.create_dialog()

    def create_dialog(self):
        """Create manufacturing view dialog"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(f"Manufacturing Details - {self.manufacturing_data['contract_number']}")
        self.window.geometry("700x600")
        self.window.configure(bg='white')
        self.window.transient(self.parent)
        self.window.grab_set()

        # Center the dialog
        self.window.update_idletasks()
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - (700 // 2)
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - (600 // 2)
        self.window.geometry(f"700x600+{x}+{y}")

        # Create scrollable content
        main_canvas = tk.Canvas(self.window, bg='white')
        scrollbar = ttk.Scrollbar(self.window, orient="vertical", command=main_canvas.yview)
        scrollable_frame = tk.Frame(main_canvas, bg='white')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: main_canvas.configure(scrollregion=main_canvas.bbox("all"))
        )

        main_canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        main_canvas.configure(yscrollcommand=scrollbar.set)

        # Title
        title_label = tk.Label(scrollable_frame, text=f"Manufacturing - {self.manufacturing_data['contract_number']}",
                              font=('Segoe UI', 18, 'bold'), bg='white', fg='#2C3E50')
        title_label.pack(pady=(20, 30))

        # Manufacturing Information
        self.create_info_section(scrollable_frame, "Manufacturing Status", [
            ("Status", self.manufacturing_data['status']),
            ("Start Date", self.manufacturing_data['start_date'] or "Not started"),
            ("Completion Date", self.manufacturing_data['completion_date'] or "Not completed"),
            ("Created", self.manufacturing_data['created_at'][:10] if self.manufacturing_data['created_at'] else "N/A"),
            ("Last Updated", self.manufacturing_data['updated_at'][:10] if self.manufacturing_data['updated_at'] else "N/A")
        ])

        # Contract Information
        self.create_info_section(scrollable_frame, "Contract Information", [
            ("Contract Number", self.manufacturing_data['contract_number']),
            ("Contract Date", self.manufacturing_data['contract_date'] or "N/A"),
            ("Signature Status", self.manufacturing_data['signature_status'])
        ])

        # Client Information
        self.create_info_section(scrollable_frame, "Client Information", [
            ("Client Name", self.manufacturing_data['client_name']),
            ("Phone", self.manufacturing_data['phone'] or "N/A"),
            ("Email", self.manufacturing_data['email'] or "N/A"),
            ("City", self.manufacturing_data['city'] or "N/A")
        ])

        # Project Specifications
        item_names = [item['item_name'] for item in self.manufacturing_data.get('items', [])]
        items_text = ', '.join(item_names) if item_names else 'N/A'

        self.create_info_section(scrollable_frame, "Project Specifications", [
            ("Item Types", items_text),
            ("Area (sqm)", f"{self.manufacturing_data['area_sqm']:.1f}"),
            ("Number of Devices", str(self.manufacturing_data['num_devices'])),
            ("Total Value", f"${self.manufacturing_data['total_amount']:,.2f}")
        ])

        # Notes and Instructions
        notes_frame = tk.Frame(scrollable_frame, bg='white', relief='solid', bd=1)
        notes_frame.pack(fill='x', padx=20, pady=(0, 20))

        tk.Label(notes_frame, text="Production Notes", font=('Segoe UI', 12, 'bold'),
                bg='white', fg='#2C3E50').pack(pady=(15, 10))

        notes_text = self.manufacturing_data.get('notes', 'No notes available')
        notes_label = tk.Label(notes_frame, text=notes_text, font=('Segoe UI', 10),
                              bg='white', fg='#7F8C8D', wraplength=600, justify='left')
        notes_label.pack(padx=20, pady=(0, 15))

        # Cutting Instructions
        cutting_frame = tk.Frame(scrollable_frame, bg='white', relief='solid', bd=1)
        cutting_frame.pack(fill='x', padx=20, pady=(0, 20))

        tk.Label(cutting_frame, text="Cutting Instructions", font=('Segoe UI', 12, 'bold'),
                bg='white', fg='#2C3E50').pack(pady=(15, 10))

        cutting_text = self.manufacturing_data.get('cutting_instructions', 'No cutting instructions available')
        cutting_label = tk.Label(cutting_frame, text=cutting_text, font=('Segoe UI', 10),
                                 bg='white', fg='#7F8C8D', wraplength=600, justify='left')
        cutting_label.pack(padx=20, pady=(0, 15))

        # Close button
        close_btn = tk.Button(scrollable_frame, text="Close", command=self.window.destroy,
                             bg='#F8F9FA', fg='#2C3E50', font=('Segoe UI', 10, 'bold'),
                             relief='flat', padx=20, pady=8)
        close_btn.pack(pady=20)

        # Pack canvas and scrollbar
        main_canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def create_info_section(self, parent, title, items):
        """Create an information section"""
        # Section frame
        section_frame = tk.Frame(parent, bg='white', relief='solid', bd=1)
        section_frame.pack(fill='x', padx=20, pady=(0, 20))

        # Section title
        title_label = tk.Label(section_frame, text=title, font=('Segoe UI', 12, 'bold'),
                              bg='white', fg='#2C3E50')
        title_label.pack(pady=(15, 10))

        # Items
        for label, value in items:
            item_frame = tk.Frame(section_frame, bg='white')
            item_frame.pack(fill='x', padx=20, pady=2)

            tk.Label(item_frame, text=f"{label}:", font=('Segoe UI', 10, 'bold'),
                    bg='white', fg='#2C3E50').pack(side='left')

            tk.Label(item_frame, text=str(value), font=('Segoe UI', 10),
                    bg='white', fg='#7F8C8D').pack(side='right')

        # Bottom padding
        tk.Frame(section_frame, bg='white', height=15).pack()

class ManufacturingNotesDialog:
    """Dialog for editing manufacturing notes and cutting instructions"""

    def __init__(self, parent, manufacturing_manager: ManufacturingManager, manufacturing_data: dict, callback):
        self.parent = parent
        self.manufacturing_manager = manufacturing_manager
        self.manufacturing_data = manufacturing_data
        self.callback = callback
        self.window = None

        self.create_dialog()

    def create_dialog(self):
        """Create manufacturing notes dialog"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(f"Edit Manufacturing Notes - {self.manufacturing_data['contract_number']}")
        self.window.geometry("600x500")
        self.window.configure(bg='white')
        self.window.transient(self.parent)
        self.window.grab_set()

        # Center the dialog
        self.window.update_idletasks()
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - (600 // 2)
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - (500 // 2)
        self.window.geometry(f"600x500+{x}+{y}")

        # Title
        title_label = tk.Label(self.window, text="Edit Manufacturing Notes",
                              font=('Segoe UI', 16, 'bold'), bg='white', fg='#2C3E50')
        title_label.pack(pady=(20, 10))

        # Contract info
        info_label = tk.Label(self.window, text=f"Contract: {self.manufacturing_data['contract_number']} - {self.manufacturing_data['client_name']}",
                             font=('Segoe UI', 12), bg='white', fg='#7F8C8D')
        info_label.pack(pady=(0, 20))

        # Production Notes
        tk.Label(self.window, text="Production Notes:", font=('Segoe UI', 12, 'bold'),
                bg='white', fg='#2C3E50').pack(anchor='w', padx=20, pady=(0, 5))

        notes_frame = tk.Frame(self.window, bg='white')
        notes_frame.pack(fill='both', expand=True, padx=20, pady=(0, 15))

        self.notes_text = tk.Text(notes_frame, height=8, font=('Segoe UI', 10), wrap='word')
        notes_scrollbar = ttk.Scrollbar(notes_frame, orient='vertical', command=self.notes_text.yview)
        self.notes_text.configure(yscrollcommand=notes_scrollbar.set)

        self.notes_text.pack(side='left', fill='both', expand=True)
        notes_scrollbar.pack(side='right', fill='y')

        # Cutting Instructions
        tk.Label(self.window, text="Cutting Instructions:", font=('Segoe UI', 12, 'bold'),
                bg='white', fg='#2C3E50').pack(anchor='w', padx=20, pady=(0, 5))

        cutting_frame = tk.Frame(self.window, bg='white')
        cutting_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

        self.cutting_text = tk.Text(cutting_frame, height=8, font=('Segoe UI', 10), wrap='word')
        cutting_scrollbar = ttk.Scrollbar(cutting_frame, orient='vertical', command=self.cutting_text.yview)
        self.cutting_text.configure(yscrollcommand=cutting_scrollbar.set)

        self.cutting_text.pack(side='left', fill='both', expand=True)
        cutting_scrollbar.pack(side='right', fill='y')

        # Buttons
        buttons_frame = tk.Frame(self.window, bg='white')
        buttons_frame.pack(fill='x', padx=20, pady=(0, 20))

        save_btn = tk.Button(buttons_frame, text="Save", command=self.save_notes,
                            bg='#4A90E2', fg='white', font=('Segoe UI', 10, 'bold'),
                            relief='flat', padx=15, pady=5)
        save_btn.pack(side='left', padx=(0, 10))

        cancel_btn = tk.Button(buttons_frame, text="Cancel", command=self.window.destroy,
                              bg='#F8F9FA', fg='#2C3E50', font=('Segoe UI', 10, 'bold'),
                              relief='flat', padx=15, pady=5)
        cancel_btn.pack(side='left')

        # Fill existing data
        if self.manufacturing_data.get('notes'):
            self.notes_text.insert('1.0', self.manufacturing_data['notes'])

        if self.manufacturing_data.get('cutting_instructions'):
            self.cutting_text.insert('1.0', self.manufacturing_data['cutting_instructions'])

        # Focus on notes field
        self.notes_text.focus()

    def save_notes(self):
        """Save manufacturing notes"""
        notes = self.notes_text.get('1.0', tk.END).strip()
        cutting_instructions = self.cutting_text.get('1.0', tk.END).strip()

        if self.manufacturing_manager.update_manufacturing_notes(
            self.manufacturing_data['id'], notes, cutting_instructions
        ):
            self.callback()
            self.window.destroy()
            messagebox.showinfo("Success", "Manufacturing notes updated successfully")
