"""
Admin Panel Module
"""

import tkinter as tk
from tkinter import messagebox, ttk
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from database.models import DatabaseManager
    from modules.auth import UserManagementWindow
except ImportError:
    import sqlite3

class ItemTypesManager:
    """Handles item types operations"""
    
    def __init__(self, db: DatabaseManager):
        self.db = db
    
    def add_item_type(self, name: str, description: str) -> bool:
        """Add a new item type"""
        try:
            # Check if item type already exists
            existing = self.db.execute_query(
                "SELECT id FROM item_types WHERE name = ?", (name,)
            )
            
            if existing:
                messagebox.showerror("Error", "Item type with this name already exists")
                return False
            
            # Add item type
            self.db.execute_update(
                "INSERT INTO item_types (name, description) VALUES (?, ?)",
                (name, description)
            )
            
            return True
        except Exception as e:
            messagebox.showerror("Error", f"Failed to add item type: {str(e)}")
            return False
    
    def update_item_type(self, item_type_id: int, name: str, description: str) -> bool:
        """Update an item type"""
        try:
            # Check if name already exists for other item types
            existing = self.db.execute_query(
                "SELECT id FROM item_types WHERE name = ? AND id != ?", (name, item_type_id)
            )
            
            if existing:
                messagebox.showerror("Error", "Item type with this name already exists")
                return False
            
            # Update item type
            self.db.execute_update(
                "UPDATE item_types SET name = ?, description = ? WHERE id = ?",
                (name, description, item_type_id)
            )
            
            return True
        except Exception as e:
            messagebox.showerror("Error", f"Failed to update item type: {str(e)}")
            return False
    
    def toggle_item_type_status(self, item_type_id: int) -> bool:
        """Toggle item type active status"""
        try:
            # Get current status
            current = self.db.execute_query("SELECT is_active FROM item_types WHERE id = ?", (item_type_id,))
            if not current:
                return False
            
            new_status = 0 if current[0]['is_active'] else 1
            
            self.db.execute_update(
                "UPDATE item_types SET is_active = ? WHERE id = ?",
                (new_status, item_type_id)
            )
            
            return True
        except Exception as e:
            messagebox.showerror("Error", f"Failed to toggle item type status: {str(e)}")
            return False
    
    def get_all_item_types(self) -> list:
        """Get all item types"""
        return self.db.execute_query("SELECT * FROM item_types ORDER BY name")
    
    def delete_item_type(self, item_type_id: int) -> bool:
        """Delete an item type"""
        try:
            # Check if item type is used in quotations
            usage = self.db.execute_query(
                "SELECT COUNT(*) as count FROM quotation_items WHERE item_type_id = ?",
                (item_type_id,)
            )
            
            if usage and usage[0]['count'] > 0:
                messagebox.showerror("Error", "Cannot delete item type that is used in quotations")
                return False
            
            self.db.execute_update("DELETE FROM item_types WHERE id = ?", (item_type_id,))
            return True
        except Exception as e:
            messagebox.showerror("Error", f"Failed to delete item type: {str(e)}")
            return False

class AdminModule:
    """Admin panel UI"""
    
    def __init__(self, parent, db: DatabaseManager, current_user: dict):
        self.parent = parent
        self.db = db
        self.current_user = current_user
        self.item_types_manager = ItemTypesManager(db)
        
        # Check admin access
        if current_user['role'] != 'Admin':
            messagebox.showerror("Access Denied", "Only administrators can access the admin panel")
            return
        
        self.create_interface()
    
    def create_interface(self):
        """Create admin interface"""
        # Header
        header_frame = tk.Frame(self.parent, bg='white')
        header_frame.pack(fill='x', padx=20, pady=(20, 10))
        
        title_label = tk.Label(header_frame, text="Admin Panel", 
                              font=('Segoe UI', 16, 'bold'), bg='white', fg='#2C3E50')
        title_label.pack(side='left')
        
        # Main content with tabs
        notebook = ttk.Notebook(self.parent)
        notebook.pack(fill='both', expand=True, padx=20, pady=10)
        
        # User Management Tab
        user_frame = tk.Frame(notebook, bg='white')
        notebook.add(user_frame, text="User Management")
        self.create_user_management_tab(user_frame)
        
        # Item Types Tab
        items_frame = tk.Frame(notebook, bg='white')
        notebook.add(items_frame, text="Item Types")
        self.create_item_types_tab(items_frame)
        
        # System Info Tab
        system_frame = tk.Frame(notebook, bg='white')
        notebook.add(system_frame, text="System Information")
        self.create_system_info_tab(system_frame)
    
    def create_user_management_tab(self, parent):
        """Create user management tab"""
        # Header
        header_frame = tk.Frame(parent, bg='white')
        header_frame.pack(fill='x', padx=20, pady=(20, 10))
        
        title_label = tk.Label(header_frame, text="User Management", 
                              font=('Segoe UI', 14, 'bold'), bg='white', fg='#2C3E50')
        title_label.pack(side='left')
        
        manage_btn = tk.Button(header_frame, text="Manage Users", 
                              command=self.open_user_management,
                              bg='#4A90E2', fg='white', font=('Segoe UI', 10, 'bold'),
                              relief='flat', padx=15, pady=5)
        manage_btn.pack(side='right')
        
        # User statistics
        stats_frame = tk.Frame(parent, bg='white', relief='solid', bd=1)
        stats_frame.pack(fill='x', padx=20, pady=10)
        
        tk.Label(stats_frame, text="User Statistics", font=('Segoe UI', 12, 'bold'),
                bg='white', fg='#2C3E50').pack(pady=(15, 10))
        
        # Get user stats
        try:
            total_users = self.db.execute_query("SELECT COUNT(*) as count FROM users")
            active_users = self.db.execute_query("SELECT COUNT(*) as count FROM users WHERE is_active = 1")
            admin_users = self.db.execute_query("SELECT COUNT(*) as count FROM users WHERE role = 'Admin' AND is_active = 1")
            sales_users = self.db.execute_query("SELECT COUNT(*) as count FROM users WHERE role = 'Sales' AND is_active = 1")
            
            stats = [
                ("Total Users", total_users[0]['count'] if total_users else 0),
                ("Active Users", active_users[0]['count'] if active_users else 0),
                ("Admin Users", admin_users[0]['count'] if admin_users else 0),
                ("Sales Users", sales_users[0]['count'] if sales_users else 0)
            ]
            
            for label, value in stats:
                stat_frame = tk.Frame(stats_frame, bg='white')
                stat_frame.pack(fill='x', padx=20, pady=2)
                
                tk.Label(stat_frame, text=f"{label}:", font=('Segoe UI', 10, 'bold'),
                        bg='white', fg='#2C3E50').pack(side='left')
                
                tk.Label(stat_frame, text=str(value), font=('Segoe UI', 10),
                        bg='white', fg='#7F8C8D').pack(side='right')
        
        except Exception as e:
            tk.Label(stats_frame, text=f"Error loading user statistics: {e}",
                    font=('Segoe UI', 10), bg='white', fg='#E74C3C').pack(padx=20, pady=10)
        
        tk.Frame(stats_frame, bg='white', height=15).pack()
    
    def create_item_types_tab(self, parent):
        """Create item types management tab"""
        # Header
        header_frame = tk.Frame(parent, bg='white')
        header_frame.pack(fill='x', padx=20, pady=(20, 10))
        
        title_label = tk.Label(header_frame, text="Item Types Management", 
                              font=('Segoe UI', 14, 'bold'), bg='white', fg='#2C3E50')
        title_label.pack(side='left')
        
        add_btn = tk.Button(header_frame, text="Add Item Type", 
                           command=self.add_item_type,
                           bg='#4A90E2', fg='white', font=('Segoe UI', 10, 'bold'),
                           relief='flat', padx=15, pady=5)
        add_btn.pack(side='right')
        
        # Item types list
        list_frame = tk.Frame(parent, bg='white', relief='solid', bd=1)
        list_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Treeview
        columns = ('ID', 'Name', 'Description', 'Status', 'Created')
        self.item_types_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=12)
        
        # Configure columns
        for col in columns:
            self.item_types_tree.heading(col, text=col)
        
        # Column widths
        self.item_types_tree.column('ID', width=50)
        self.item_types_tree.column('Name', width=150)
        self.item_types_tree.column('Description', width=250)
        self.item_types_tree.column('Status', width=80)
        self.item_types_tree.column('Created', width=100)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.item_types_tree.yview)
        self.item_types_tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack
        self.item_types_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        scrollbar.pack(side='right', fill='y', pady=10)
        
        # Buttons
        buttons_frame = tk.Frame(parent, bg='white')
        buttons_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        edit_btn = tk.Button(buttons_frame, text="Edit", command=self.edit_item_type,
                            bg='#17A2B8', fg='white', font=('Segoe UI', 10, 'bold'),
                            relief='flat', padx=15, pady=5)
        edit_btn.pack(side='left', padx=(0, 10))
        
        toggle_btn = tk.Button(buttons_frame, text="Toggle Status", command=self.toggle_item_type,
                              bg='#F39C12', fg='white', font=('Segoe UI', 10, 'bold'),
                              relief='flat', padx=15, pady=5)
        toggle_btn.pack(side='left', padx=(0, 10))
        
        delete_btn = tk.Button(buttons_frame, text="Delete", command=self.delete_item_type,
                              bg='#E74C3C', fg='white', font=('Segoe UI', 10, 'bold'),
                              relief='flat', padx=15, pady=5)
        delete_btn.pack(side='left')
        
        # Load item types
        self.load_item_types()
        
        # Bind double-click to edit
        self.item_types_tree.bind('<Double-1>', lambda e: self.edit_item_type())
    
    def create_system_info_tab(self, parent):
        """Create system information tab"""
        # Header
        header_frame = tk.Frame(parent, bg='white')
        header_frame.pack(fill='x', padx=20, pady=(20, 10))
        
        title_label = tk.Label(header_frame, text="System Information", 
                              font=('Segoe UI', 14, 'bold'), bg='white', fg='#2C3E50')
        title_label.pack(side='left')
        
        # System info
        info_frame = tk.Frame(parent, bg='white', relief='solid', bd=1)
        info_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        tk.Label(info_frame, text="Application Information", font=('Segoe UI', 12, 'bold'),
                bg='white', fg='#2C3E50').pack(pady=(15, 10))
        
        # Get database stats
        try:
            quotations_count = self.db.execute_query("SELECT COUNT(*) as count FROM quotations")
            contracts_count = self.db.execute_query("SELECT COUNT(*) as count FROM contracts")
            manufacturing_count = self.db.execute_query("SELECT COUNT(*) as count FROM manufacturing")
            installations_count = self.db.execute_query("SELECT COUNT(*) as count FROM installations")
            inventory_count = self.db.execute_query("SELECT COUNT(*) as count FROM inventory")
            
            system_stats = [
                ("Application Name", "Beyond Smart Glass Tracker"),
                ("Version", "1.0.0"),
                ("Database", "SQLite"),
                ("", ""),
                ("Total Quotations", quotations_count[0]['count'] if quotations_count else 0),
                ("Total Contracts", contracts_count[0]['count'] if contracts_count else 0),
                ("Manufacturing Records", manufacturing_count[0]['count'] if manufacturing_count else 0),
                ("Installation Records", installations_count[0]['count'] if installations_count else 0),
                ("Inventory Items", inventory_count[0]['count'] if inventory_count else 0)
            ]
            
            for label, value in system_stats:
                if label == "":  # Spacer
                    tk.Frame(info_frame, bg='white', height=10).pack()
                    continue
                    
                stat_frame = tk.Frame(info_frame, bg='white')
                stat_frame.pack(fill='x', padx=20, pady=2)
                
                tk.Label(stat_frame, text=f"{label}:", font=('Segoe UI', 10, 'bold'),
                        bg='white', fg='#2C3E50').pack(side='left')
                
                tk.Label(stat_frame, text=str(value), font=('Segoe UI', 10),
                        bg='white', fg='#7F8C8D').pack(side='right')
        
        except Exception as e:
            tk.Label(info_frame, text=f"Error loading system information: {e}",
                    font=('Segoe UI', 10), bg='white', fg='#E74C3C').pack(padx=20, pady=10)
        
        tk.Frame(info_frame, bg='white', height=15).pack()
    
    def open_user_management(self):
        """Open user management window"""
        try:
            from modules.auth import AuthManager
            auth_manager = AuthManager(self.db)
            UserManagementWindow(self.parent, auth_manager, self.current_user)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open user management: {str(e)}")
    
    def load_item_types(self):
        """Load item types into the tree"""
        try:
            # Clear existing items
            for item in self.item_types_tree.get_children():
                self.item_types_tree.delete(item)
            
            # Load item types
            item_types = self.item_types_manager.get_all_item_types()
            
            for item_type in item_types:
                status = "Active" if item_type['is_active'] else "Inactive"
                created_date = item_type['created_at'][:10] if item_type['created_at'] else ""
                
                self.item_types_tree.insert('', 'end', values=(
                    item_type['id'],
                    item_type['name'],
                    item_type['description'] or "",
                    status,
                    created_date
                ))
        except Exception as e:
            print(f"Error loading item types: {e}")
    
    def get_selected_item_type_id(self):
        """Get selected item type ID"""
        selection = self.item_types_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select an item type")
            return None
        
        item = self.item_types_tree.item(selection[0])
        return item['values'][0]
    
    def add_item_type(self):
        """Add new item type"""
        ItemTypeDialog(self.parent, self.item_types_manager, None, self.load_item_types)
    
    def edit_item_type(self):
        """Edit selected item type"""
        item_type_id = self.get_selected_item_type_id()
        if item_type_id:
            item_types = self.item_types_manager.db.execute_query("SELECT * FROM item_types WHERE id = ?", (item_type_id,))
            if item_types:
                ItemTypeDialog(self.parent, self.item_types_manager, item_types[0], self.load_item_types)
    
    def toggle_item_type(self):
        """Toggle item type status"""
        item_type_id = self.get_selected_item_type_id()
        if item_type_id:
            if self.item_types_manager.toggle_item_type_status(item_type_id):
                self.load_item_types()
                messagebox.showinfo("Success", "Item type status updated")
    
    def delete_item_type(self):
        """Delete selected item type"""
        item_type_id = self.get_selected_item_type_id()
        if item_type_id:
            if messagebox.askyesno("Confirm Delete", "Are you sure you want to delete this item type?"):
                if self.item_types_manager.delete_item_type(item_type_id):
                    self.load_item_types()
                    messagebox.showinfo("Success", "Item type deleted successfully")

class ItemTypeDialog:
    """Dialog for adding/editing item types"""

    def __init__(self, parent, item_types_manager: ItemTypesManager, item_type_data: dict, callback):
        self.parent = parent
        self.item_types_manager = item_types_manager
        self.item_type_data = item_type_data
        self.callback = callback
        self.window = None

        self.create_dialog()

    def create_dialog(self):
        """Create item type dialog"""
        self.window = tk.Toplevel(self.parent)
        title = "Edit Item Type" if self.item_type_data else "Add Item Type"
        self.window.title(title)
        self.window.geometry("400x250")
        self.window.configure(bg='white')
        self.window.transient(self.parent)
        self.window.grab_set()

        # Center the dialog
        self.window.update_idletasks()
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - (400 // 2)
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - (250 // 2)
        self.window.geometry(f"400x250+{x}+{y}")

        # Title
        title_label = tk.Label(self.window, text=title, font=('Segoe UI', 16, 'bold'),
                              bg='white', fg='#2C3E50')
        title_label.pack(pady=(20, 30))

        # Form fields
        # Name
        tk.Label(self.window, text="Name *:", font=('Segoe UI', 10), bg='white').pack(anchor='w', padx=40, pady=(0, 5))
        self.name_entry = tk.Entry(self.window, width=30, font=('Segoe UI', 10))
        self.name_entry.pack(padx=40, pady=(0, 15))

        # Description
        tk.Label(self.window, text="Description:", font=('Segoe UI', 10), bg='white').pack(anchor='w', padx=40, pady=(0, 5))
        self.description_entry = tk.Entry(self.window, width=30, font=('Segoe UI', 10))
        self.description_entry.pack(padx=40, pady=(0, 20))

        # Buttons
        buttons_frame = tk.Frame(self.window, bg='white')
        buttons_frame.pack(pady=(0, 20))

        save_btn = tk.Button(buttons_frame, text="Save", command=self.save_item_type,
                            bg='#4A90E2', fg='white', font=('Segoe UI', 10, 'bold'),
                            relief='flat', padx=15, pady=5)
        save_btn.pack(side='left', padx=(0, 10))

        cancel_btn = tk.Button(buttons_frame, text="Cancel", command=self.window.destroy,
                              bg='#F8F9FA', fg='#2C3E50', font=('Segoe UI', 10, 'bold'),
                              relief='flat', padx=15, pady=5)
        cancel_btn.pack(side='left')

        # Fill form if editing
        if self.item_type_data:
            self.fill_form()

        # Focus on first field
        self.name_entry.focus()

    def fill_form(self):
        """Fill form with existing item type data"""
        data = self.item_type_data

        self.name_entry.insert(0, data['name'])
        if data['description']:
            self.description_entry.insert(0, data['description'])

    def validate_form(self):
        """Validate form data"""
        if not self.name_entry.get().strip():
            messagebox.showerror("Error", "Item type name is required")
            return False

        return True

    def save_item_type(self):
        """Save item type data"""
        if not self.validate_form():
            return

        name = self.name_entry.get().strip()
        description = self.description_entry.get().strip()

        if self.item_type_data:
            # Update existing item type
            if self.item_types_manager.update_item_type(self.item_type_data['id'], name, description):
                self.callback()
                self.window.destroy()
                messagebox.showinfo("Success", "Item type updated successfully")
        else:
            # Create new item type
            if self.item_types_manager.add_item_type(name, description):
                self.callback()
                self.window.destroy()
                messagebox.showinfo("Success", "Item type added successfully")
