"""
Inventory Management Module
"""

import tkinter as tk
from tkinter import messagebox, ttk
from datetime import datetime
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from database.models import DatabaseManager
except ImportError:
    import sqlite3

class InventoryManager:
    """Handles inventory operations"""

    def __init__(self, db: DatabaseManager):
        self.db = db

    def add_inventory_item(self, item_name: str, item_type: str, initial_stock: int, min_alert: int, unit: str) -> bool:
        """Add a new inventory item"""
        try:
            # Check if item already exists
            existing = self.db.execute_query(
                "SELECT id FROM inventory WHERE item_name = ?", (item_name,)
            )

            if existing:
                messagebox.showerror("Error", "Item with this name already exists")
                return False

            # Add item
            item_id = self.db.get_last_insert_id(
                """INSERT INTO inventory (item_name, item_type, current_stock, min_stock_alert, unit)
                   VALUES (?, ?, ?, ?, ?)""",
                (item_name, item_type, initial_stock, min_alert, unit)
            )

            # Record initial stock transaction
            if initial_stock > 0:
                self.add_stock_transaction(item_id, 'Add', initial_stock, 'Initial stock', None)

            return True
        except Exception as e:
            messagebox.showerror("Error", f"Failed to add inventory item: {str(e)}")
            return False

    def update_inventory_item(self, item_id: int, item_name: str, item_type: str, min_alert: int, unit: str) -> bool:
        """Update inventory item details"""
        try:
            self.db.execute_update(
                """UPDATE inventory SET item_name = ?, item_type = ?, min_stock_alert = ?, unit = ?,
                   updated_at = CURRENT_TIMESTAMP WHERE id = ?""",
                (item_name, item_type, min_alert, unit, item_id)
            )
            return True
        except Exception as e:
            messagebox.showerror("Error", f"Failed to update inventory item: {str(e)}")
            return False

    def add_stock_transaction(self, item_id: int, transaction_type: str, quantity: int, reason: str, user_id: int) -> bool:
        """Add a stock transaction"""
        try:
            # Get current stock
            current = self.db.execute_query("SELECT current_stock FROM inventory WHERE id = ?", (item_id,))
            if not current:
                return False

            current_stock = current[0]['current_stock']

            # Calculate new stock
            if transaction_type == 'Add':
                new_stock = current_stock + quantity
            elif transaction_type == 'Remove':
                new_stock = current_stock - quantity
                if new_stock < 0:
                    messagebox.showerror("Error", "Insufficient stock")
                    return False
            else:  # Adjust
                new_stock = quantity

            # Update stock
            self.db.execute_update(
                "UPDATE inventory SET current_stock = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
                (new_stock, item_id)
            )

            # Record transaction
            self.db.execute_update(
                """INSERT INTO inventory_transactions
                   (inventory_id, transaction_type, quantity, reason, user_id)
                   VALUES (?, ?, ?, ?, ?)""",
                (item_id, transaction_type, quantity, reason, user_id)
            )

            return True
        except Exception as e:
            messagebox.showerror("Error", f"Failed to add stock transaction: {str(e)}")
            return False

    def get_inventory_items(self, item_type_filter=None, low_stock_only=False) -> list:
        """Get inventory items with optional filters"""
        query = "SELECT * FROM inventory"
        params = ()
        conditions = []

        if item_type_filter:
            conditions.append("item_type = ?")
            params = params + (item_type_filter,)

        if low_stock_only:
            conditions.append("current_stock <= min_stock_alert")

        if conditions:
            query += " WHERE " + " AND ".join(conditions)

        query += " ORDER BY item_name"

        return self.db.execute_query(query, params)

    def get_inventory_transactions(self, item_id: int = None, limit: int = 50) -> list:
        """Get inventory transactions"""
        query = """
            SELECT it.*, i.item_name, u.full_name as user_name
            FROM inventory_transactions it
            JOIN inventory i ON it.inventory_id = i.id
            LEFT JOIN users u ON it.user_id = u.id
        """
        params = ()

        if item_id:
            query += " WHERE it.inventory_id = ?"
            params = (item_id,)

        query += " ORDER BY it.transaction_date DESC"

        if limit:
            query += f" LIMIT {limit}"

        return self.db.execute_query(query, params)

    def delete_inventory_item(self, item_id: int) -> bool:
        """Delete an inventory item"""
        try:
            # Check if item has transactions
            transactions = self.db.execute_query(
                "SELECT COUNT(*) as count FROM inventory_transactions WHERE inventory_id = ?",
                (item_id,)
            )

            if transactions and transactions[0]['count'] > 0:
                messagebox.showerror("Error", "Cannot delete item with transaction history")
                return False

            self.db.execute_update("DELETE FROM inventory WHERE id = ?", (item_id,))
            return True
        except Exception as e:
            messagebox.showerror("Error", f"Failed to delete inventory item: {str(e)}")
            return False

class InventoryModule:
    """Inventory module UI"""

    def __init__(self, parent, db: DatabaseManager, current_user: dict):
        self.parent = parent
        self.db = db
        self.current_user = current_user
        self.inventory_manager = InventoryManager(db)
        self.inventory_tree = None
        self.type_filter_var = None

        self.create_interface()

    def create_interface(self):
        """Create inventory interface"""
        # Header
        header_frame = tk.Frame(self.parent, bg='white')
        header_frame.pack(fill='x', padx=20, pady=(20, 10))

        title_label = tk.Label(header_frame, text="Inventory Management",
                              font=('Segoe UI', 16, 'bold'), bg='white', fg='#2C3E50')
        title_label.pack(side='left')

        # Header buttons
        btn_frame = tk.Frame(header_frame, bg='white')
        btn_frame.pack(side='right')

        new_btn = tk.Button(btn_frame, text="Add Item", command=self.add_item,
                           bg='#4A90E2', fg='white', font=('Segoe UI', 10, 'bold'),
                           relief='flat', padx=15, pady=5)
        new_btn.pack(side='left', padx=(0, 10))

        stock_btn = tk.Button(btn_frame, text="Adjust Stock", command=self.adjust_stock,
                             bg='#F39C12', fg='white', font=('Segoe UI', 10, 'bold'),
                             relief='flat', padx=15, pady=5)
        stock_btn.pack(side='left', padx=(0, 10))

        refresh_btn = tk.Button(btn_frame, text="Refresh", command=self.load_inventory,
                               bg='#F8F9FA', fg='#2C3E50', font=('Segoe UI', 10, 'bold'),
                               relief='flat', padx=15, pady=5)
        refresh_btn.pack(side='left')

        # Filters
        filter_frame = tk.Frame(self.parent, bg='white')
        filter_frame.pack(fill='x', padx=20, pady=(0, 10))

        tk.Label(filter_frame, text="Type Filter:", font=('Segoe UI', 10), bg='white').pack(side='left', padx=(0, 10))

        self.type_filter_var = tk.StringVar(value="All")
        type_combo = ttk.Combobox(filter_frame, textvariable=self.type_filter_var,
                                 values=["All", "Smart Film Roll", "Device", "Remote"],
                                 state="readonly", width=15)
        type_combo.pack(side='left', padx=(0, 10))
        type_combo.bind('<<ComboboxSelected>>', lambda e: self.load_inventory())

        # Low stock filter
        self.low_stock_var = tk.BooleanVar()
        low_stock_cb = tk.Checkbutton(filter_frame, text="Show only low stock items",
                                     variable=self.low_stock_var, command=self.load_inventory,
                                     bg='white', font=('Segoe UI', 10))
        low_stock_cb.pack(side='left', padx=(20, 0))

        # Inventory list
        list_frame = tk.Frame(self.parent, bg='white', relief='solid', bd=1)
        list_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Treeview
        columns = ('ID', 'Item Name', 'Type', 'Current Stock', 'Min Alert', 'Unit', 'Status', 'Last Updated')
        self.inventory_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

        # Configure columns
        for col in columns:
            self.inventory_tree.heading(col, text=col)

        # Column widths
        self.inventory_tree.column('ID', width=50)
        self.inventory_tree.column('Item Name', width=200)
        self.inventory_tree.column('Type', width=120)
        self.inventory_tree.column('Current Stock', width=100)
        self.inventory_tree.column('Min Alert', width=80)
        self.inventory_tree.column('Unit', width=60)
        self.inventory_tree.column('Status', width=80)
        self.inventory_tree.column('Last Updated', width=100)

        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.inventory_tree.yview)
        self.inventory_tree.configure(yscrollcommand=scrollbar.set)

        # Pack
        self.inventory_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        scrollbar.pack(side='right', fill='y', pady=10)

        # Buttons
        buttons_frame = tk.Frame(self.parent, bg='white')
        buttons_frame.pack(fill='x', padx=20, pady=(0, 20))

        edit_btn = tk.Button(buttons_frame, text="Edit Item", command=self.edit_item,
                            bg='#4A90E2', fg='white', font=('Segoe UI', 10, 'bold'),
                            relief='flat', padx=15, pady=5)
        edit_btn.pack(side='left', padx=(0, 10))

        history_btn = tk.Button(buttons_frame, text="View History", command=self.view_history,
                               bg='#17A2B8', fg='white', font=('Segoe UI', 10, 'bold'),
                               relief='flat', padx=15, pady=5)
        history_btn.pack(side='left', padx=(0, 10))

        delete_btn = tk.Button(buttons_frame, text="Delete Item", command=self.delete_item,
                              bg='#E74C3C', fg='white', font=('Segoe UI', 10, 'bold'),
                              relief='flat', padx=15, pady=5)
        delete_btn.pack(side='left')

        # Load inventory
        self.load_inventory()

        # Bind double-click to edit
        self.inventory_tree.bind('<Double-1>', lambda e: self.edit_item())

    def load_inventory(self):
        """Load inventory items into the tree"""
        try:
            # Clear existing items
            for item in self.inventory_tree.get_children():
                self.inventory_tree.delete(item)

            # Get filters
            type_filter = self.type_filter_var.get() if self.type_filter_var.get() != "All" else None
            low_stock_only = self.low_stock_var.get()

            # Load inventory
            items = self.inventory_manager.get_inventory_items(type_filter, low_stock_only)

            for item in items:
                # Determine status
                if item['current_stock'] <= item['min_stock_alert']:
                    status = "Low Stock"
                    status_color = "#E74C3C"
                elif item['current_stock'] <= item['min_stock_alert'] * 1.5:
                    status = "Warning"
                    status_color = "#F39C12"
                else:
                    status = "OK"
                    status_color = "#27AE60"

                updated_date = item['updated_at'][:10] if item['updated_at'] else ""

                item_id = self.inventory_tree.insert('', 'end', values=(
                    item['id'],
                    item['item_name'],
                    item['item_type'],
                    item['current_stock'],
                    item['min_stock_alert'],
                    item['unit'],
                    status,
                    updated_date
                ))

                # Color code based on status
                if status == "Low Stock":
                    self.inventory_tree.set(item_id, 'Status', status)

        except Exception as e:
            print(f"Error loading inventory: {e}")

    def get_selected_item_id(self):
        """Get selected inventory item ID"""
        selection = self.inventory_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select an inventory item")
            return None

        item = self.inventory_tree.item(selection[0])
        return item['values'][0]

    def add_item(self):
        """Add new inventory item"""
        InventoryItemDialog(self.parent, self.inventory_manager, None, self.load_inventory)

    def edit_item(self):
        """Edit selected inventory item"""
        item_id = self.get_selected_item_id()
        if item_id:
            items = self.inventory_manager.db.execute_query("SELECT * FROM inventory WHERE id = ?", (item_id,))
            if items:
                InventoryItemDialog(self.parent, self.inventory_manager, items[0], self.load_inventory)

    def adjust_stock(self):
        """Adjust stock for selected item"""
        item_id = self.get_selected_item_id()
        if item_id:
            items = self.inventory_manager.db.execute_query("SELECT * FROM inventory WHERE id = ?", (item_id,))
            if items:
                StockAdjustmentDialog(self.parent, self.inventory_manager, items[0], self.current_user, self.load_inventory)

    def view_history(self):
        """View transaction history for selected item"""
        item_id = self.get_selected_item_id()
        if item_id:
            items = self.inventory_manager.db.execute_query("SELECT * FROM inventory WHERE id = ?", (item_id,))
            if items:
                TransactionHistoryDialog(self.parent, self.inventory_manager, items[0])

    def delete_item(self):
        """Delete selected inventory item"""
        item_id = self.get_selected_item_id()
        if item_id:
            if messagebox.askyesno("Confirm Delete", "Are you sure you want to delete this inventory item?"):
                if self.inventory_manager.delete_inventory_item(item_id):
                    self.load_inventory()
                    messagebox.showinfo("Success", "Inventory item deleted successfully")

class InventoryItemDialog:
    """Dialog for adding/editing inventory items"""

    def __init__(self, parent, inventory_manager: InventoryManager, item_data: dict, callback):
        self.parent = parent
        self.inventory_manager = inventory_manager
        self.item_data = item_data
        self.callback = callback
        self.window = None

        self.create_dialog()

    def create_dialog(self):
        """Create inventory item dialog"""
        self.window = tk.Toplevel(self.parent)
        title = "Edit Inventory Item" if self.item_data else "Add Inventory Item"
        self.window.title(title)
        self.window.geometry("400x350")
        self.window.configure(bg='white')
        self.window.transient(self.parent)
        self.window.grab_set()

        # Center the dialog
        self.window.update_idletasks()
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - (400 // 2)
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - (350 // 2)
        self.window.geometry(f"400x350+{x}+{y}")

        # Title
        title_label = tk.Label(self.window, text=title, font=('Segoe UI', 16, 'bold'),
                              bg='white', fg='#2C3E50')
        title_label.pack(pady=(20, 30))

        # Form fields
        # Item Name
        tk.Label(self.window, text="Item Name *:", font=('Segoe UI', 10), bg='white').pack(anchor='w', padx=40, pady=(0, 5))
        self.name_entry = tk.Entry(self.window, width=30, font=('Segoe UI', 10))
        self.name_entry.pack(padx=40, pady=(0, 15))

        # Item Type
        tk.Label(self.window, text="Item Type *:", font=('Segoe UI', 10), bg='white').pack(anchor='w', padx=40, pady=(0, 5))
        self.type_var = tk.StringVar(value="Smart Film Roll")
        type_combo = ttk.Combobox(self.window, textvariable=self.type_var,
                                 values=["Smart Film Roll", "Device", "Remote"],
                                 state="readonly", width=27)
        type_combo.pack(padx=40, pady=(0, 15))

        # Initial Stock (only for new items)
        if not self.item_data:
            tk.Label(self.window, text="Initial Stock *:", font=('Segoe UI', 10), bg='white').pack(anchor='w', padx=40, pady=(0, 5))
            self.stock_entry = tk.Entry(self.window, width=30, font=('Segoe UI', 10))
            self.stock_entry.pack(padx=40, pady=(0, 15))
            self.stock_entry.insert(0, "0")

        # Min Stock Alert
        tk.Label(self.window, text="Min Stock Alert *:", font=('Segoe UI', 10), bg='white').pack(anchor='w', padx=40, pady=(0, 5))
        self.min_alert_entry = tk.Entry(self.window, width=30, font=('Segoe UI', 10))
        self.min_alert_entry.pack(padx=40, pady=(0, 15))
        self.min_alert_entry.insert(0, "10")

        # Unit
        tk.Label(self.window, text="Unit:", font=('Segoe UI', 10), bg='white').pack(anchor='w', padx=40, pady=(0, 5))
        self.unit_entry = tk.Entry(self.window, width=30, font=('Segoe UI', 10))
        self.unit_entry.pack(padx=40, pady=(0, 15))
        self.unit_entry.insert(0, "pcs")

        # Buttons
        buttons_frame = tk.Frame(self.window, bg='white')
        buttons_frame.pack(pady=(20, 20))

        save_btn = tk.Button(buttons_frame, text="Save", command=self.save_item,
                            bg='#4A90E2', fg='white', font=('Segoe UI', 10, 'bold'),
                            relief='flat', padx=15, pady=5)
        save_btn.pack(side='left', padx=(0, 10))

        cancel_btn = tk.Button(buttons_frame, text="Cancel", command=self.window.destroy,
                              bg='#F8F9FA', fg='#2C3E50', font=('Segoe UI', 10, 'bold'),
                              relief='flat', padx=15, pady=5)
        cancel_btn.pack(side='left')

        # Fill form if editing
        if self.item_data:
            self.fill_form()

        # Focus on first field
        self.name_entry.focus()

    def fill_form(self):
        """Fill form with existing item data"""
        data = self.item_data

        self.name_entry.insert(0, data['item_name'])
        self.type_var.set(data['item_type'])
        self.min_alert_entry.delete(0, tk.END)
        self.min_alert_entry.insert(0, str(data['min_stock_alert']))
        self.unit_entry.delete(0, tk.END)
        self.unit_entry.insert(0, data['unit'])

    def validate_form(self):
        """Validate form data"""
        if not self.name_entry.get().strip():
            messagebox.showerror("Error", "Item name is required")
            return False

        try:
            if not self.item_data:  # New item
                stock = int(self.stock_entry.get())
                if stock < 0:
                    raise ValueError()
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid initial stock")
            return False

        try:
            min_alert = int(self.min_alert_entry.get())
            if min_alert < 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid minimum stock alert")
            return False

        return True

    def save_item(self):
        """Save inventory item"""
        if not self.validate_form():
            return

        item_name = self.name_entry.get().strip()
        item_type = self.type_var.get()
        min_alert = int(self.min_alert_entry.get())
        unit = self.unit_entry.get().strip() or "pcs"

        if self.item_data:
            # Update existing item
            if self.inventory_manager.update_inventory_item(self.item_data['id'], item_name, item_type, min_alert, unit):
                self.callback()
                self.window.destroy()
                messagebox.showinfo("Success", "Inventory item updated successfully")
        else:
            # Create new item
            initial_stock = int(self.stock_entry.get())
            if self.inventory_manager.add_inventory_item(item_name, item_type, initial_stock, min_alert, unit):
                self.callback()
                self.window.destroy()
                messagebox.showinfo("Success", "Inventory item added successfully")

class StockAdjustmentDialog:
    """Dialog for adjusting stock levels"""

    def __init__(self, parent, inventory_manager: InventoryManager, item_data: dict, current_user: dict, callback):
        self.parent = parent
        self.inventory_manager = inventory_manager
        self.item_data = item_data
        self.current_user = current_user
        self.callback = callback
        self.window = None

        self.create_dialog()

    def create_dialog(self):
        """Create stock adjustment dialog"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(f"Adjust Stock - {self.item_data['item_name']}")
        self.window.geometry("400x300")
        self.window.configure(bg='white')
        self.window.transient(self.parent)
        self.window.grab_set()

        # Center the dialog
        self.window.update_idletasks()
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - (400 // 2)
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - (300 // 2)
        self.window.geometry(f"400x300+{x}+{y}")

        # Title
        title_label = tk.Label(self.window, text=f"Adjust Stock", font=('Segoe UI', 16, 'bold'),
                              bg='white', fg='#2C3E50')
        title_label.pack(pady=(20, 10))

        # Item info
        info_label = tk.Label(self.window, text=f"Item: {self.item_data['item_name']}",
                             font=('Segoe UI', 12), bg='white', fg='#7F8C8D')
        info_label.pack(pady=(0, 5))

        current_label = tk.Label(self.window, text=f"Current Stock: {self.item_data['current_stock']} {self.item_data['unit']}",
                                font=('Segoe UI', 12, 'bold'), bg='white', fg='#2C3E50')
        current_label.pack(pady=(0, 20))

        # Transaction Type
        tk.Label(self.window, text="Transaction Type:", font=('Segoe UI', 10), bg='white').pack(anchor='w', padx=40, pady=(0, 5))
        self.type_var = tk.StringVar(value="Add")
        type_frame = tk.Frame(self.window, bg='white')
        type_frame.pack(padx=40, pady=(0, 15))

        tk.Radiobutton(type_frame, text="Add Stock", variable=self.type_var, value="Add",
                      bg='white', fg='#2C3E50', font=('Segoe UI', 10)).pack(side='left')
        tk.Radiobutton(type_frame, text="Remove Stock", variable=self.type_var, value="Remove",
                      bg='white', fg='#2C3E50', font=('Segoe UI', 10)).pack(side='left', padx=(20, 0))
        tk.Radiobutton(type_frame, text="Set Exact", variable=self.type_var, value="Adjust",
                      bg='white', fg='#2C3E50', font=('Segoe UI', 10)).pack(side='left', padx=(20, 0))

        # Quantity
        tk.Label(self.window, text="Quantity:", font=('Segoe UI', 10), bg='white').pack(anchor='w', padx=40, pady=(0, 5))
        self.quantity_entry = tk.Entry(self.window, width=30, font=('Segoe UI', 10))
        self.quantity_entry.pack(padx=40, pady=(0, 15))

        # Reason
        tk.Label(self.window, text="Reason:", font=('Segoe UI', 10), bg='white').pack(anchor='w', padx=40, pady=(0, 5))
        self.reason_entry = tk.Entry(self.window, width=30, font=('Segoe UI', 10))
        self.reason_entry.pack(padx=40, pady=(0, 20))

        # Buttons
        buttons_frame = tk.Frame(self.window, bg='white')
        buttons_frame.pack(pady=(0, 20))

        save_btn = tk.Button(buttons_frame, text="Apply", command=self.apply_adjustment,
                            bg='#4A90E2', fg='white', font=('Segoe UI', 10, 'bold'),
                            relief='flat', padx=15, pady=5)
        save_btn.pack(side='left', padx=(0, 10))

        cancel_btn = tk.Button(buttons_frame, text="Cancel", command=self.window.destroy,
                              bg='#F8F9FA', fg='#2C3E50', font=('Segoe UI', 10, 'bold'),
                              relief='flat', padx=15, pady=5)
        cancel_btn.pack(side='left')

        # Focus on quantity field
        self.quantity_entry.focus()

    def apply_adjustment(self):
        """Apply stock adjustment"""
        try:
            quantity = int(self.quantity_entry.get())
            if quantity <= 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid quantity")
            return

        transaction_type = self.type_var.get()
        reason = self.reason_entry.get().strip() or "Manual adjustment"

        if self.inventory_manager.add_stock_transaction(
            self.item_data['id'], transaction_type, quantity, reason, self.current_user['id']
        ):
            self.callback()
            self.window.destroy()
            messagebox.showinfo("Success", "Stock adjustment applied successfully")

class TransactionHistoryDialog:
    """Dialog for viewing transaction history"""

    def __init__(self, parent, inventory_manager: InventoryManager, item_data: dict):
        self.parent = parent
        self.inventory_manager = inventory_manager
        self.item_data = item_data
        self.window = None

        self.create_dialog()

    def create_dialog(self):
        """Create transaction history dialog"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(f"Transaction History - {self.item_data['item_name']}")
        self.window.geometry("700x500")
        self.window.configure(bg='white')
        self.window.transient(self.parent)
        self.window.grab_set()

        # Center the dialog
        self.window.update_idletasks()
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - (700 // 2)
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - (500 // 2)
        self.window.geometry(f"700x500+{x}+{y}")

        # Title
        title_label = tk.Label(self.window, text=f"Transaction History", font=('Segoe UI', 16, 'bold'),
                              bg='white', fg='#2C3E50')
        title_label.pack(pady=(20, 10))

        # Item info
        info_label = tk.Label(self.window, text=f"Item: {self.item_data['item_name']}",
                             font=('Segoe UI', 12), bg='white', fg='#7F8C8D')
        info_label.pack(pady=(0, 20))

        # Transactions list
        list_frame = tk.Frame(self.window, bg='white', relief='solid', bd=1)
        list_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

        # Treeview
        columns = ('Date', 'Type', 'Quantity', 'Reason', 'User')
        self.transactions_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

        # Configure columns
        for col in columns:
            self.transactions_tree.heading(col, text=col)

        # Column widths
        self.transactions_tree.column('Date', width=120)
        self.transactions_tree.column('Type', width=80)
        self.transactions_tree.column('Quantity', width=80)
        self.transactions_tree.column('Reason', width=200)
        self.transactions_tree.column('User', width=120)

        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.transactions_tree.yview)
        self.transactions_tree.configure(yscrollcommand=scrollbar.set)

        # Pack
        self.transactions_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        scrollbar.pack(side='right', fill='y', pady=10)

        # Close button
        close_btn = tk.Button(self.window, text="Close", command=self.window.destroy,
                             bg='#F8F9FA', fg='#2C3E50', font=('Segoe UI', 10, 'bold'),
                             relief='flat', padx=20, pady=8)
        close_btn.pack(pady=(0, 20))

        # Load transactions
        self.load_transactions()

    def load_transactions(self):
        """Load transaction history"""
        try:
            # Clear existing items
            for item in self.transactions_tree.get_children():
                self.transactions_tree.delete(item)

            # Load transactions
            transactions = self.inventory_manager.get_inventory_transactions(self.item_data['id'])

            for transaction in transactions:
                date_str = transaction['transaction_date'][:16] if transaction['transaction_date'] else ""
                user_name = transaction['user_name'] or "System"

                self.transactions_tree.insert('', 'end', values=(
                    date_str,
                    transaction['transaction_type'],
                    transaction['quantity'],
                    transaction['reason'] or "",
                    user_name
                ))
        except Exception as e:
            print(f"Error loading transactions: {e}")