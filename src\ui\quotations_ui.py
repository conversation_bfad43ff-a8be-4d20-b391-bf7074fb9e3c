"""
Modern Quotations UI Module for Beyond Smart Glass Tracker
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
from src.ui.styles import COLORS, FONTS, BUTTON_STYLE, BUTTON_SECONDARY_STYLE, CARD_STYLE, SIDEBAR_BUTTON_STYLE
from src.ui.quotation_form import QuotationForm


class QuotationsUI:
    def __init__(self, parent):
        self.parent = parent
        self.quotations_data = []  # Sample data storage
        self.create_interface()
        self.load_sample_data()
        
    def create_interface(self):
        """Create modern quotations interface"""
        # Clear parent
        for widget in self.parent.winfo_children():
            widget.destroy()
            
        # Main container
        main_frame = tk.Frame(self.parent, bg=COLORS['background'])
        main_frame.pack(fill='both', expand=True)
        
        # Header section
        self.create_header(main_frame)
        
        # Content area
        content_frame = tk.Frame(main_frame, bg=COLORS['background'])
        content_frame.pack(fill='both', expand=True, padx=30, pady=(0, 30))
        
        # Statistics cards
        self.create_stats_cards(content_frame)
        
        # Quotations table
        self.create_quotations_table(content_frame)
        
    def create_header(self, parent):
        """Create header with title and buttons"""
        header_frame = tk.Frame(parent, bg=COLORS['surface'], height=80)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        # Header content
        header_content = tk.Frame(header_frame, bg=COLORS['surface'])
        header_content.pack(fill='both', expand=True, padx=30, pady=20)
        
        # Title
        title_label = tk.Label(
            header_content,
            text="إدارة العروض",
            font=FONTS['heading'],
            bg=COLORS['surface'],
            fg=COLORS['text_primary']
        )
        title_label.pack(side='left')
        
        # Buttons frame
        buttons_frame = tk.Frame(header_content, bg=COLORS['surface'])
        buttons_frame.pack(side='right')
        
        # New Quotation button
        new_btn = tk.Button(
            buttons_frame,
            text="عرض جديد",
            command=self.new_quotation,
            **BUTTON_STYLE
        )
        new_btn.pack(side='left', padx=(0, 15))
        
        # Refresh button
        refresh_btn = tk.Button(
            buttons_frame,
            text="تحديث",
            command=self.refresh_data,
            **BUTTON_SECONDARY_STYLE
        )
        refresh_btn.pack(side='left')
        
    def create_stats_cards(self, parent):
        """Create statistics cards"""
        stats_frame = tk.Frame(parent, bg=COLORS['background'])
        stats_frame.pack(fill='x', pady=(20, 30))
        
        # Statistics data
        stats = [
            {"title": "إجمالي العروض", "value": "7", "color": COLORS['primary']},
            {"title": "العروض المقبولة", "value": "3", "color": COLORS['success']},
            {"title": "قيد المراجعة", "value": "2", "color": COLORS['warning']},
            {"title": "هذا الشهر", "value": "4", "color": COLORS['info']}
        ]
        
        for i, stat in enumerate(stats):
            self.create_stat_card(stats_frame, stat, i)
            
    def create_stat_card(self, parent, stat_data, index):
        """Create individual statistics card"""
        card_frame = tk.Frame(
            parent,
            bg=COLORS['surface'],
            relief='flat',
            bd=0
        )
        card_frame.pack(side='left', fill='both', expand=True, padx=(0, 20) if index < 3 else (0, 0))
        
        # Add border effect
        card_frame.configure(highlightbackground=COLORS['border'], highlightthickness=1)
        
        # Card content
        content_frame = tk.Frame(card_frame, bg=COLORS['surface'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Value
        value_label = tk.Label(
            content_frame,
            text=stat_data['value'],
            font=('Segoe UI', 24, 'bold'),
            bg=COLORS['surface'],
            fg=stat_data['color']
        )
        value_label.pack()
        
        # Title
        title_label = tk.Label(
            content_frame,
            text=stat_data['title'],
            font=FONTS['default'],
            bg=COLORS['surface'],
            fg=COLORS['text_secondary']
        )
        title_label.pack()
        
    def create_quotations_table(self, parent):
        """Create quotations table"""
        # Table container
        table_frame = tk.Frame(
            parent,
            bg=COLORS['surface'],
            relief='flat',
            bd=0
        )
        table_frame.pack(fill='both', expand=True)
        table_frame.configure(highlightbackground=COLORS['border'], highlightthickness=1)
        
        # Table header
        header_frame = tk.Frame(table_frame, bg=COLORS['surface'])
        header_frame.pack(fill='x', padx=20, pady=(20, 0))
        
        table_title = tk.Label(
            header_frame,
            text="قائمة العروض",
            font=FONTS['subheading'],
            bg=COLORS['surface'],
            fg=COLORS['text_primary']
        )
        table_title.pack(side='left')
        
        # Search frame
        search_frame = tk.Frame(header_frame, bg=COLORS['surface'])
        search_frame.pack(side='right')
        
        search_entry = tk.Entry(
            search_frame,
            font=FONTS['default'],
            bg='white',
            fg=COLORS['text_primary'],
            relief='solid',
            bd=1,
            width=25
        )
        search_entry.pack(side='left', padx=(0, 10))
        search_entry.insert(0, "البحث...")
        
        # Treeview
        tree_frame = tk.Frame(table_frame, bg=COLORS['surface'])
        tree_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Configure treeview style
        style = ttk.Style()
        style.theme_use('clam')
        style.configure("Treeview",
                       background='white',
                       foreground=COLORS['text_primary'],
                       fieldbackground='white',
                       font=FONTS['default'],
                       rowheight=35)
        style.configure("Treeview.Heading",
                       background=COLORS['primary'],
                       foreground='white',
                       font=FONTS['subheading'],
                       relief='flat')
        style.map("Treeview.Heading",
                 background=[('active', COLORS['primary_dark'])])
        style.map("Treeview",
                 background=[('selected', COLORS['primary_light'])])
        
        # Create treeview
        columns = ('ID', 'Client', 'Phone', 'City', 'Total', 'Status', 'Created')
        self.tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=12)
        
        # Configure columns
        column_widths = {'ID': 60, 'Client': 150, 'Phone': 120, 'City': 100, 'Total': 100, 'Status': 100, 'Created': 100}
        column_names = {'ID': 'الرقم', 'Client': 'العميل', 'Phone': 'الهاتف', 'City': 'المدينة', 'Total': 'المبلغ', 'Status': 'الحالة', 'Created': 'التاريخ'}
        
        for col in columns:
            self.tree.heading(col, text=column_names[col])
            self.tree.column(col, width=column_widths[col], anchor='center')
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack treeview and scrollbar
        self.tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # Bind double-click
        self.tree.bind('<Double-1>', self.view_quotation)
        
    def load_sample_data(self):
        """Load sample quotations data"""
        sample_data = [
            {'id': 'Q001', 'client': 'أحمد محمد', 'phone': '0501234567', 'city': 'الرياض', 'total': '15,000', 'status': 'جديد', 'created': '2025-01-15'},
            {'id': 'Q002', 'client': 'فاطمة علي', 'phone': '0509876543', 'city': 'جدة', 'total': '22,500', 'status': 'مقبول', 'created': '2025-01-14'},
            {'id': 'Q003', 'client': 'محمد السعد', 'phone': '0551234567', 'city': 'الدمام', 'total': '18,750', 'status': 'قيد المراجعة', 'created': '2025-01-13'},
            {'id': 'Q004', 'client': 'نورا أحمد', 'phone': '0561234567', 'city': 'الرياض', 'total': '12,000', 'status': 'مرفوض', 'created': '2025-01-12'},
            {'id': 'Q005', 'client': 'خالد محمد', 'phone': '0571234567', 'city': 'مكة', 'total': '28,000', 'status': 'مقبول', 'created': '2025-01-11'},
        ]
        
        # Clear existing data
        for item in self.tree.get_children():
            self.tree.delete(item)
            
        # Insert sample data
        for data in sample_data:
            self.tree.insert('', 'end', values=(
                data['id'], data['client'], data['phone'], 
                data['city'], data['total'], data['status'], data['created']
            ))
            
    def new_quotation(self):
        """Open new quotation form"""
        form = QuotationForm(self.parent, callback=self.on_quotation_saved)
        form.show()
        
    def on_quotation_saved(self, data):
        """Handle quotation saved callback"""
        # Add to data storage (in real app, save to database)
        new_id = f"Q{len(self.quotations_data) + 6:03d}"
        quotation = {
            'id': new_id,
            'client': data['client_name'],
            'phone': data['phone'],
            'city': data['city'],
            'total': data['total_amount'],
            'status': data['status'],
            'created': datetime.now().strftime('%Y-%m-%d')
        }
        self.quotations_data.append(quotation)
        
        # Refresh the display
        self.refresh_data()
        
    def view_quotation(self, event=None):
        """View selected quotation details"""
        selection = self.tree.selection()
        if not selection:
            return
            
        item = self.tree.item(selection[0])
        values = item['values']
        
        # Show quotation details
        details = f"""
تفاصيل العرض:

الرقم: {values[0]}
العميل: {values[1]}
الهاتف: {values[2]}
المدينة: {values[3]}
المبلغ: {values[4]} ريال
الحالة: {values[5]}
تاريخ الإنشاء: {values[6]}
        """
        
        messagebox.showinfo("تفاصيل العرض", details)
        
    def refresh_data(self):
        """Refresh quotations data"""
        self.load_sample_data()
        messagebox.showinfo("تم", "تم تحديث البيانات بنجاح!")
