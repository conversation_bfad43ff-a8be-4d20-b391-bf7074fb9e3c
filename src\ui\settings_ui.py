"""
Settings UI Module for Beyond Smart Glass Tracker
"""

import tkinter as tk
from tkinter import ttk, messagebox
from src.ui.styles import COLORS, FONTS, BUTTON_STYLE, BUTTON_SECONDARY_STYLE, CARD_STYLE
from src.utils.language import language_manager, _
from src.utils.dropdown_manager import dropdown_manager


class SettingsUI:
    def __init__(self, parent, on_language_change=None):
        self.parent = parent
        self.on_language_change = on_language_change
        self.create_interface()
        
    def create_interface(self):
        """Create settings interface"""
        # Clear parent
        for widget in self.parent.winfo_children():
            widget.destroy()
            
        # Main container
        main_frame = tk.Frame(self.parent, bg=COLORS['background'])
        main_frame.pack(fill='both', expand=True)
        
        # Header section
        self.create_header(main_frame)
        
        # Content area with tabs
        content_frame = tk.Frame(main_frame, bg=COLORS['background'])
        content_frame.pack(fill='both', expand=True, padx=30, pady=(0, 30))
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(content_frame)
        self.notebook.pack(fill='both', expand=True)
        
        # Language settings tab
        self.create_language_tab()
        
        # Dropdown management tab
        self.create_dropdown_tab()
        
    def create_header(self, parent):
        """Create header with title"""
        header_frame = tk.Frame(parent, bg=COLORS['surface'], height=80)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        # Header content
        header_content = tk.Frame(header_frame, bg=COLORS['surface'])
        header_content.pack(fill='both', expand=True, padx=30, pady=20)
        
        # Title
        title_label = tk.Label(
            header_content,
            text=_('settings'),
            font=FONTS['heading'],
            bg=COLORS['surface'],
            fg=COLORS['text_primary']
        )
        title_label.pack(side='left')
        
    def create_language_tab(self):
        """Create language settings tab"""
        # Create tab frame
        lang_frame = tk.Frame(self.notebook, bg=COLORS['background'])
        self.notebook.add(lang_frame, text=_('language_settings'))
        
        # Language selection card
        lang_card = tk.Frame(
            lang_frame,
            bg=COLORS['surface'],
            relief='flat',
            bd=0
        )
        lang_card.pack(fill='x', padx=20, pady=20)
        lang_card.configure(highlightbackground=COLORS['border'], highlightthickness=1)
        
        # Card content
        card_content = tk.Frame(lang_card, bg=COLORS['surface'])
        card_content.pack(fill='both', expand=True, padx=30, pady=30)
        
        # Title
        title_label = tk.Label(
            card_content,
            text=_('language'),
            font=FONTS['subheading'],
            bg=COLORS['surface'],
            fg=COLORS['text_primary']
        )
        title_label.pack(anchor='w', pady=(0, 20))
        
        # Language selection
        self.language_var = tk.StringVar(value=language_manager.get_current_language())
        
        # Arabic option
        ar_radio = tk.Radiobutton(
            card_content,
            text=_('arabic'),
            variable=self.language_var,
            value='ar',
            font=FONTS['default'],
            bg=COLORS['surface'],
            fg=COLORS['text_primary'],
            selectcolor=COLORS['primary'],
            command=self.change_language
        )
        ar_radio.pack(anchor='w', pady=5)
        
        # English option
        en_radio = tk.Radiobutton(
            card_content,
            text=_('english'),
            variable=self.language_var,
            value='en',
            font=FONTS['default'],
            bg=COLORS['surface'],
            fg=COLORS['text_primary'],
            selectcolor=COLORS['primary'],
            command=self.change_language
        )
        en_radio.pack(anchor='w', pady=5)
        
    def create_dropdown_tab(self):
        """Create dropdown management tab"""
        # Create tab frame
        dropdown_frame = tk.Frame(self.notebook, bg=COLORS['background'])
        self.notebook.add(dropdown_frame, text=_('dropdown_management'))
        
        # Main container
        main_container = tk.Frame(dropdown_frame, bg=COLORS['background'])
        main_container.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Left panel - Categories
        left_panel = tk.Frame(
            main_container,
            bg=COLORS['surface'],
            relief='flat',
            bd=0,
            width=300
        )
        left_panel.pack(side='left', fill='y', padx=(0, 10))
        left_panel.pack_propagate(False)
        left_panel.configure(highlightbackground=COLORS['border'], highlightthickness=1)
        
        # Categories header
        cat_header = tk.Frame(left_panel, bg=COLORS['surface'])
        cat_header.pack(fill='x', padx=20, pady=(20, 10))
        
        cat_title = tk.Label(
            cat_header,
            text="Categories",
            font=FONTS['subheading'],
            bg=COLORS['surface'],
            fg=COLORS['text_primary']
        )
        cat_title.pack(side='left')
        
        # Categories list
        cat_list_frame = tk.Frame(left_panel, bg=COLORS['surface'])
        cat_list_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))
        
        # Categories listbox
        self.categories_listbox = tk.Listbox(
            cat_list_frame,
            font=FONTS['default'],
            bg='white',
            fg=COLORS['text_primary'],
            selectbackground=COLORS['primary'],
            selectforeground='white',
            relief='solid',
            bd=1
        )
        self.categories_listbox.pack(fill='both', expand=True)
        self.categories_listbox.bind('<<ListboxSelect>>', self.on_category_select)
        
        # Right panel - Items
        right_panel = tk.Frame(
            main_container,
            bg=COLORS['surface'],
            relief='flat',
            bd=0
        )
        right_panel.pack(side='right', fill='both', expand=True)
        right_panel.configure(highlightbackground=COLORS['border'], highlightthickness=1)
        
        # Items header
        items_header = tk.Frame(right_panel, bg=COLORS['surface'])
        items_header.pack(fill='x', padx=20, pady=(20, 10))
        
        items_title = tk.Label(
            items_header,
            text=_('item_management'),
            font=FONTS['subheading'],
            bg=COLORS['surface'],
            fg=COLORS['text_primary']
        )
        items_title.pack(side='left')
        
        # Add item button
        add_btn = tk.Button(
            items_header,
            text=_('add_item'),
            command=self.add_item_dialog,
            **BUTTON_STYLE
        )
        add_btn.pack(side='right')
        
        # Items list
        items_list_frame = tk.Frame(right_panel, bg=COLORS['surface'])
        items_list_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))
        
        # Create treeview for items
        columns = ('Arabic', 'English')
        self.items_tree = ttk.Treeview(items_list_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        self.items_tree.heading('Arabic', text='العربية')
        self.items_tree.heading('English', text='English')
        self.items_tree.column('Arabic', width=200)
        self.items_tree.column('English', width=200)
        
        # Scrollbar for items
        items_scrollbar = ttk.Scrollbar(items_list_frame, orient='vertical', command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=items_scrollbar.set)
        
        # Pack items tree and scrollbar
        self.items_tree.pack(side='left', fill='both', expand=True)
        items_scrollbar.pack(side='right', fill='y')
        
        # Context menu for items
        self.create_items_context_menu()
        
        # Load initial data
        self.load_categories()
        
    def create_items_context_menu(self):
        """Create context menu for items tree"""
        self.items_context_menu = tk.Menu(self.parent, tearoff=0)
        self.items_context_menu.add_command(label=_('edit'), command=self.edit_item_dialog)
        self.items_context_menu.add_command(label=_('delete'), command=self.delete_item)
        
        self.items_tree.bind('<Button-3>', self.show_items_context_menu)
        
    def show_items_context_menu(self, event):
        """Show context menu for items"""
        item = self.items_tree.identify_row(event.y)
        if item:
            self.items_tree.selection_set(item)
            self.items_context_menu.post(event.x_root, event.y_root)
            
    def load_categories(self):
        """Load categories into listbox"""
        self.categories_listbox.delete(0, tk.END)
        categories = dropdown_manager.get_categories()
        for category in categories:
            self.categories_listbox.insert(tk.END, category)
            
    def on_category_select(self, event):
        """Handle category selection"""
        selection = self.categories_listbox.curselection()
        if selection:
            category = self.categories_listbox.get(selection[0])
            self.load_items(category)
            
    def load_items(self, category):
        """Load items for selected category"""
        # Clear existing items
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)
            
        # Load items
        ar_items = dropdown_manager.get_items(category, 'ar')
        en_items = dropdown_manager.get_items(category, 'en')
        
        # Ensure both lists have same length
        max_len = max(len(ar_items), len(en_items))
        ar_items.extend([''] * (max_len - len(ar_items)))
        en_items.extend([''] * (max_len - len(en_items)))
        
        # Insert items
        for ar_item, en_item in zip(ar_items, en_items):
            self.items_tree.insert('', 'end', values=(ar_item, en_item))
            
    def add_item_dialog(self):
        """Show add item dialog"""
        selection = self.categories_listbox.curselection()
        if not selection:
            messagebox.showwarning(_('warning'), "Please select a category first")
            return
            
        category = self.categories_listbox.get(selection[0])
        self.show_item_dialog(category)
        
    def edit_item_dialog(self):
        """Show edit item dialog"""
        selection = self.items_tree.selection()
        if not selection:
            return
            
        item = self.items_tree.item(selection[0])
        values = item['values']
        
        cat_selection = self.categories_listbox.curselection()
        if not cat_selection:
            return
            
        category = self.categories_listbox.get(cat_selection[0])
        self.show_item_dialog(category, values[0], values[1])
        
    def show_item_dialog(self, category, ar_text='', en_text=''):
        """Show item add/edit dialog"""
        dialog = tk.Toplevel(self.parent)
        dialog.title(_('add_item') if not ar_text else _('edit_item'))
        dialog.geometry("400x300")
        dialog.configure(bg=COLORS['background'])
        dialog.transient(self.parent)
        dialog.grab_set()
        
        # Main frame
        main_frame = tk.Frame(dialog, bg=COLORS['background'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Arabic input
        tk.Label(main_frame, text="Arabic:", font=FONTS['default'], 
                bg=COLORS['background'], fg=COLORS['text_primary']).pack(anchor='w', pady=(0, 5))
        ar_entry = tk.Entry(main_frame, font=FONTS['default'], bg='white', 
                           fg=COLORS['text_primary'], relief='solid', bd=1)
        ar_entry.pack(fill='x', pady=(0, 15))
        ar_entry.insert(0, ar_text)
        
        # English input
        tk.Label(main_frame, text="English:", font=FONTS['default'], 
                bg=COLORS['background'], fg=COLORS['text_primary']).pack(anchor='w', pady=(0, 5))
        en_entry = tk.Entry(main_frame, font=FONTS['default'], bg='white', 
                           fg=COLORS['text_primary'], relief='solid', bd=1)
        en_entry.pack(fill='x', pady=(0, 20))
        en_entry.insert(0, en_text)
        
        # Buttons
        btn_frame = tk.Frame(main_frame, bg=COLORS['background'])
        btn_frame.pack(fill='x')
        
        def save_item():
            ar_val = ar_entry.get().strip()
            en_val = en_entry.get().strip()
            
            if not ar_val or not en_val:
                messagebox.showerror(_('error'), _('fill_required_fields'))
                return
                
            if ar_text:  # Edit mode
                if dropdown_manager.update_item(category, ar_text, en_text, ar_val, en_val):
                    self.load_items(category)
                    dialog.destroy()
                    messagebox.showinfo(_('success'), _('data_updated'))
                else:
                    messagebox.showerror(_('error'), "Failed to update item")
            else:  # Add mode
                if dropdown_manager.add_item(category, ar_val, en_val):
                    self.load_items(category)
                    dialog.destroy()
                    messagebox.showinfo(_('success'), "Item added successfully!")
                else:
                    messagebox.showerror(_('error'), "Failed to add item")
        
        tk.Button(btn_frame, text=_('cancel'), command=dialog.destroy, 
                 **BUTTON_SECONDARY_STYLE).pack(side='left')
        tk.Button(btn_frame, text=_('save'), command=save_item, 
                 **BUTTON_STYLE).pack(side='right')
                 
    def delete_item(self):
        """Delete selected item"""
        selection = self.items_tree.selection()
        if not selection:
            return
            
        item = self.items_tree.item(selection[0])
        values = item['values']
        
        cat_selection = self.categories_listbox.curselection()
        if not cat_selection:
            return
            
        category = self.categories_listbox.get(cat_selection[0])
        
        if messagebox.askyesno(_('warning'), f"Delete item: {values[0]} / {values[1]}?"):
            if dropdown_manager.remove_item(category, values[0], values[1]):
                self.load_items(category)
                messagebox.showinfo(_('success'), "Item deleted successfully!")
            else:
                messagebox.showerror(_('error'), "Failed to delete item")
                
    def change_language(self):
        """Handle language change"""
        new_language = self.language_var.get()
        language_manager.set_language(new_language)
        
        if self.on_language_change:
            self.on_language_change(new_language)
            
        messagebox.showinfo(_('success'), _('data_updated'))
