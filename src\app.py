"""
Main Application Class for Beyond Smart Glass Tracker
"""

import tkinter as tk
from tkinter import messagebox, ttk
import sys
import os

# Add src to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from database.models import DatabaseManager
    from ui.styles import *
    from ui.base_widgets import *
except ImportError:
    # Fallback for basic functionality
    import sqlite3

    # Define basic colors
    COLORS = {
        'primary': '#4A90E2',
        'background': '#FFFFFF',
        'surface': '#F5F7FA',
        'text_primary': '#2C3E50',
        'text_secondary': '#7F8C8D',
        'border': '#E1E8ED',
        'hover': '#EBF3FD'
    }

    WINDOW_CONFIG = {
        'min_width': 1200,
        'min_height': 800,
        'bg': COLORS['surface']
    }

class BeyondSmartGlassApp:
    """Main application class"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.db = DatabaseManager()
        self.current_user = None
        self.current_module = None
        
        self.setup_window()
        self.create_login_screen()
    
    def setup_window(self):
        """Setup main window properties"""
        self.root.title("Beyond Smart Glass Tracker")
        self.root.geometry("1200x800")
        self.root.minsize(WINDOW_CONFIG['min_width'], WINDOW_CONFIG['min_height'])
        self.root.configure(bg=WINDOW_CONFIG['bg'])
        
        # Center window on screen
        self.center_window()
        
        # Configure grid weights
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)
    
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_login_screen(self):
        """Create login interface"""
        # Clear any existing widgets
        for widget in self.root.winfo_children():
            widget.destroy()

        # Main login frame
        login_frame = tk.Frame(self.root, bg='white', relief='solid', bd=1)
        login_frame.place(relx=0.5, rely=0.5, anchor='center', width=400, height=300)

        # Title
        title_label = tk.Label(login_frame, text="Beyond Smart Glass Tracker",
                              font=('Segoe UI', 16, 'bold'), fg=COLORS['primary'], bg='white')
        title_label.pack(pady=(30, 20))

        # Subtitle
        subtitle_label = tk.Label(login_frame, text="Please login to continue",
                                 font=('Segoe UI', 10), fg=COLORS['text_secondary'], bg='white')
        subtitle_label.pack(pady=(0, 30))

        # Username field
        username_label = tk.Label(login_frame, text="Username:", font=('Segoe UI', 10), bg='white')
        username_label.pack(anchor='w', padx=40, pady=(0, 5))

        self.username_entry = tk.Entry(login_frame, width=25, font=('Segoe UI', 10))
        self.username_entry.pack(padx=40, pady=(0, 15))

        # Password field
        password_label = tk.Label(login_frame, text="Password:", font=('Segoe UI', 10), bg='white')
        password_label.pack(anchor='w', padx=40, pady=(0, 5))

        self.password_entry = tk.Entry(login_frame, width=25, show="*", font=('Segoe UI', 10))
        self.password_entry.pack(padx=40, pady=(0, 20))

        # Login button
        login_btn = tk.Button(login_frame, text="Login", command=self.handle_login,
                             bg=COLORS['primary'], fg='white', font=('Segoe UI', 10, 'bold'),
                             relief='flat', padx=20, pady=8)
        login_btn.pack(pady=(0, 20))

        # Bind Enter key to login
        self.root.bind('<Return>', lambda e: self.handle_login())

        # Focus on username entry
        self.username_entry.focus()
    
    def handle_login(self):
        """Handle login attempt"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not username or not password:
            messagebox.showerror("Error", "Please enter both username and password")
            return
        
        # Verify credentials
        import hashlib
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        
        users = self.db.execute_query(
            "SELECT * FROM users WHERE username = ? AND password_hash = ? AND is_active = 1",
            (username, password_hash)
        )
        
        if users:
            self.current_user = users[0]
            self.create_main_interface()
        else:
            messagebox.showerror("Error", "Invalid username or password")
            self.password_entry.delete(0, tk.END)
    
    def create_main_interface(self):
        """Create main application interface"""
        # Clear login screen
        for widget in self.root.winfo_children():
            widget.destroy()
        
        # Unbind Enter key
        self.root.unbind('<Return>')
        
        # Create main layout
        self.create_menu_bar()
        self.create_navigation()
        self.create_content_area()
        self.create_status_bar()

        # Load default module (Dashboard)
        self.load_module('dashboard')

    def create_menu_bar(self):
        """Create application menu bar"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)

        # Export submenu
        export_menu = tk.Menu(file_menu, tearoff=0)
        file_menu.add_cascade(label="Export", menu=export_menu)
        export_menu.add_command(label="Export Quotations to CSV", command=self.export_quotations)
        export_menu.add_command(label="Export Contracts to CSV", command=self.export_contracts)
        export_menu.add_command(label="Export Inventory to CSV", command=self.export_inventory)
        export_menu.add_separator()
        export_menu.add_command(label="Export All Data", command=self.export_all_data)

        # Import submenu
        import_menu = tk.Menu(file_menu, tearoff=0)
        file_menu.add_cascade(label="Import", menu=import_menu)
        import_menu.add_command(label="Import Quotations from CSV", command=self.import_quotations)

        file_menu.add_separator()

        # Backup submenu
        backup_menu = tk.Menu(file_menu, tearoff=0)
        file_menu.add_cascade(label="Backup", menu=backup_menu)
        backup_menu.add_command(label="Create Database Backup", command=self.backup_database)
        backup_menu.add_command(label="Restore from Backup", command=self.restore_database)

        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)

        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)

    def export_quotations(self):
        """Export quotations to CSV"""
        try:
            from utils.data_management import DataManager
            data_manager = DataManager(self.db)
            data_manager.export_quotations_to_csv()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export quotations: {str(e)}")

    def export_contracts(self):
        """Export contracts to CSV"""
        try:
            from utils.data_management import DataManager
            data_manager = DataManager(self.db)
            data_manager.export_contracts_to_csv()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export contracts: {str(e)}")

    def export_inventory(self):
        """Export inventory to CSV"""
        try:
            from utils.data_management import DataManager
            data_manager = DataManager(self.db)
            data_manager.export_inventory_to_csv()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export inventory: {str(e)}")

    def export_all_data(self):
        """Export all data"""
        try:
            from utils.data_management import DataManager
            data_manager = DataManager(self.db)
            data_manager.export_all_data()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export all data: {str(e)}")

    def import_quotations(self):
        """Import quotations from CSV"""
        try:
            from utils.data_management import DataManager
            data_manager = DataManager(self.db)
            data_manager.import_quotations_from_csv()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to import quotations: {str(e)}")

    def backup_database(self):
        """Create database backup"""
        try:
            from utils.data_management import DataManager
            data_manager = DataManager(self.db)
            data_manager.backup_database()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to create backup: {str(e)}")

    def restore_database(self):
        """Restore database from backup"""
        try:
            from utils.data_management import DataManager
            data_manager = DataManager(self.db)
            if data_manager.restore_database():
                messagebox.showinfo("Restart Required",
                                   "Database restored successfully.\nPlease restart the application to see changes.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to restore database: {str(e)}")

    def show_about(self):
        """Show about dialog"""
        about_text = """Beyond Smart Glass Tracker v1.0.0

A comprehensive business management system for smart glass operations.

Features:
• Quotations Management
• Contract Tracking
• Manufacturing Progress
• Installation Scheduling
• Inventory Management
• Sales Dashboard
• User Management

Developed with Python and Tkinter
© 2024 Beyond Smart Glass"""

        messagebox.showinfo("About Beyond Smart Glass Tracker", about_text)
    
    def create_navigation(self):
        """Create navigation sidebar"""
        self.nav_frame = tk.Frame(self.root, bg=COLORS['surface'], width=200)
        self.nav_frame.grid(row=0, column=0, sticky='nsew', padx=(0, 1))
        self.nav_frame.grid_propagate(False)

        # Configure grid
        self.root.grid_columnconfigure(1, weight=1)

        # Logo/Title area
        logo_frame = tk.Frame(self.nav_frame, bg=COLORS['primary'])
        logo_frame.pack(fill='x', pady=(0, 1))

        logo_label = tk.Label(logo_frame, text="Beyond Smart Glass",
                             font=('Segoe UI', 12, 'bold'), fg='white', bg=COLORS['primary'])
        logo_label.pack(pady=15)

        # Navigation buttons
        self.nav_buttons = {}
        nav_items = [
            ('dashboard', 'Dashboard'),
            ('quotations', 'Quotations'),
            ('contracts', 'Contracts'),
            ('manufacturing', 'Manufacturing'),
            ('installation', 'Installation'),
            ('inventory', 'Inventory'),
            ('admin', 'Admin Panel')
        ]

        for module_id, label in nav_items:
            # Skip admin panel for non-admin users
            if module_id == 'admin' and self.current_user['role'] != 'Admin':
                continue

            btn = tk.Button(
                self.nav_frame,
                text=label,
                command=lambda m=module_id: self.load_module(m),
                bg=COLORS['surface'], fg=COLORS['text_primary'],
                font=('Segoe UI', 10, 'bold'), relief='flat',
                anchor='w', padx=20, pady=10
            )
            btn.pack(fill='x', padx=5, pady=2)
            self.nav_buttons[module_id] = btn

        # Logout button at bottom
        logout_btn = tk.Button(self.nav_frame, text="Logout", command=self.logout,
                              bg='#F8F9FA', fg=COLORS['text_primary'],
                              font=('Segoe UI', 10, 'bold'), relief='flat', padx=20, pady=10)
        logout_btn.pack(side='bottom', fill='x', padx=5, pady=10)
    
    def create_content_area(self):
        """Create main content area"""
        self.content_frame = tk.Frame(self.root, bg='white')
        self.content_frame.grid(row=0, column=1, sticky='nsew', padx=1)

        # Configure grid weights
        self.content_frame.grid_rowconfigure(0, weight=1)
        self.content_frame.grid_columnconfigure(0, weight=1)

    def create_status_bar(self):
        """Create status bar"""
        self.status_bar = tk.Frame(self.root, bg=COLORS['surface'], height=30)
        self.status_bar.grid(row=1, column=0, columnspan=2, sticky='ew')

        # Status label
        self.status_label = tk.Label(self.status_bar, text="Ready",
                                    font=('Segoe UI', 9), bg=COLORS['surface'])
        self.status_label.pack(side='left', padx=10, pady=5)

        # User info label
        user_text = f"Logged in as: {self.current_user['username']} ({self.current_user['role']})"
        self.user_label = tk.Label(self.status_bar, text=user_text,
                                  font=('Segoe UI', 9), bg=COLORS['surface'])
        self.user_label.pack(side='right', padx=10, pady=5)
    
    def load_module(self, module_name):
        """Load a specific module"""
        # Clear current content
        for widget in self.content_frame.winfo_children():
            widget.destroy()

        # Update navigation buttons
        for btn_id, btn in self.nav_buttons.items():
            if btn_id == module_name:
                btn.config(bg=COLORS['primary'], fg='white')
            else:
                btn.config(bg=COLORS['surface'], fg=COLORS['text_primary'])

        self.current_module = module_name

        # Create placeholder content for now
        self.create_placeholder_content(module_name)

        # Update status
        self.status_label.config(text=f"Viewing {module_name.replace('_', ' ').title()}")
    
    def create_placeholder_content(self, module_name):
        """Create placeholder content for modules"""
        title = module_name.replace('_', ' ').title()

        try:
            # Try to load the actual module
            if module_name == 'dashboard':
                from modules.dashboard import DashboardModule
                DashboardModule(self.content_frame, self.db, self.current_user)
            elif module_name == 'quotations':
                self.create_simple_quotations_interface()
            elif module_name == 'contracts':
                from modules.contracts import ContractsModule
                ContractsModule(self.content_frame, self.db, self.current_user)
            elif module_name == 'manufacturing':
                from modules.manufacturing import ManufacturingModule
                ManufacturingModule(self.content_frame, self.db, self.current_user)
            elif module_name == 'installation':
                from modules.installation import InstallationModule
                InstallationModule(self.content_frame, self.db, self.current_user)
            elif module_name == 'inventory':
                from modules.inventory import InventoryModule
                InventoryModule(self.content_frame, self.db, self.current_user)
            elif module_name == 'admin':
                from modules.admin import AdminModule
                AdminModule(self.content_frame, self.db, self.current_user)
            else:
                # Show placeholder for other modules
                self.create_default_placeholder(title)

        except Exception as e:
            print(f"Error loading module {module_name}: {e}")
            self.create_default_placeholder(title)

    def create_default_placeholder(self, title):
        """Create default placeholder content"""
        # Header
        header_frame = tk.Frame(self.content_frame, bg='white')
        header_frame.pack(fill='x', padx=20, pady=(20, 10))

        title_label = tk.Label(header_frame, text=title, font=('Segoe UI', 16, 'bold'),
                              bg='white', fg='#2C3E50')
        title_label.pack(side='left')

        # Content area
        content_area = tk.Frame(self.content_frame, bg='white', relief='solid', bd=1)
        content_area.pack(fill='both', expand=True, padx=20, pady=10)

        # Placeholder message
        placeholder_label = tk.Label(content_area,
                                    text=f"{title} module will be implemented here",
                                    font=('Segoe UI', 12), fg='#7F8C8D', bg='white')
        placeholder_label.pack(expand=True)

    def create_simple_quotations_interface(self):
        """Create a simple quotations interface"""
        # Header
        header_frame = tk.Frame(self.content_frame, bg='white')
        header_frame.pack(fill='x', padx=20, pady=(20, 10))

        title_label = tk.Label(header_frame, text="Quotations Management",
                              font=('Segoe UI', 16, 'bold'), bg='white', fg='#2C3E50')
        title_label.pack(side='left')

        # Buttons
        btn_frame = tk.Frame(header_frame, bg='white')
        btn_frame.pack(side='right')

        new_btn = tk.Button(btn_frame, text="New Quotation", bg='#4A90E2', fg='white',
                           font=('Segoe UI', 10, 'bold'), relief='flat', padx=15, pady=5,
                           command=self.show_new_quotation_form)
        new_btn.pack(side='left', padx=(0, 10))

        refresh_btn = tk.Button(btn_frame, text="Refresh", bg='#F8F9FA', fg='#2C3E50',
                               font=('Segoe UI', 10, 'bold'), relief='flat', padx=15, pady=5,
                               command=self.load_quotations_list)
        refresh_btn.pack(side='left')

        # Quotations list
        list_frame = tk.Frame(self.content_frame, bg='white', relief='solid', bd=1)
        list_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Treeview
        columns = ('ID', 'Client', 'Phone', 'City', 'Total', 'Status', 'Created')
        self.quotations_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

        # Configure columns
        for col in columns:
            self.quotations_tree.heading(col, text=col)

        # Column widths
        self.quotations_tree.column('ID', width=50)
        self.quotations_tree.column('Client', width=150)
        self.quotations_tree.column('Phone', width=120)
        self.quotations_tree.column('City', width=100)
        self.quotations_tree.column('Total', width=100)
        self.quotations_tree.column('Status', width=80)
        self.quotations_tree.column('Created', width=100)

        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.quotations_tree.yview)
        self.quotations_tree.configure(yscrollcommand=scrollbar.set)

        # Pack
        self.quotations_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        scrollbar.pack(side='right', fill='y', pady=10)

        # Load quotations
        self.load_quotations_list()

    def load_quotations_list(self):
        """Load quotations into the tree"""
        try:
            # Clear existing items
            for item in self.quotations_tree.get_children():
                self.quotations_tree.delete(item)

            # Load quotations
            quotations = self.db.execute_query(
                "SELECT * FROM quotations ORDER BY created_at DESC"
            )

            for quotation in quotations:
                created_date = quotation['created_at'][:10] if quotation['created_at'] else ""
                total_formatted = f"${quotation['total_amount']:,.2f}"

                self.quotations_tree.insert('', 'end', values=(
                    quotation['id'],
                    quotation['client_name'],
                    quotation['phone'] or "",
                    quotation['city'] or "",
                    total_formatted,
                    quotation['status'],
                    created_date
                ))
        except Exception as e:
            print(f"Error loading quotations: {e}")

    def show_new_quotation_form(self):
        """Show new quotation form"""
        messagebox.showinfo("Info", "New quotation form will be implemented in the next version")
    
    def logout(self):
        """Handle logout"""
        if messagebox.askyesno("Logout", "Are you sure you want to logout?"):
            self.current_user = None
            self.current_module = None
            self.create_login_screen()
    
    def run(self):
        """Start the application"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.root.quit()
        except Exception as e:
            messagebox.showerror("Error", f"An unexpected error occurred: {str(e)}")
            self.root.quit()
