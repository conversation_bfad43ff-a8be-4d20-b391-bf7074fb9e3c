"""
Database models and schema definitions for Beyond Smart Glass Tracker
"""

import sqlite3
import os
from datetime import datetime
from typing import Optional, List, Dict, Any

class DatabaseManager:
    def __init__(self, db_path: str = "beyond_smart_glass.db"):
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self):
        """Get database connection"""
        return sqlite3.connect(self.db_path)
    
    def init_database(self):
        """Initialize database with all required tables"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                role TEXT NOT NULL CHECK (role IN ('Admin', 'Sales')),
                full_name TEXT NOT NULL,
                email TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        # Item types table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS item_types (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Quotations table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS quotations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                client_name TEXT NOT NULL,
                phone TEXT,
                email TEXT,
                city TEXT,
                area_sqm REAL NOT NULL,
                num_devices INTEGER NOT NULL,
                price_per_sqm REAL NOT NULL,
                discount REAL DEFAULT 0,
                total_amount REAL NOT NULL,
                status TEXT DEFAULT 'Draft' CHECK (status IN ('Draft', 'Sent', 'Accepted', 'Rejected')),
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        ''')
        
        # Quotation items (many-to-many relationship)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS quotation_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                quotation_id INTEGER NOT NULL,
                item_type_id INTEGER NOT NULL,
                quantity INTEGER DEFAULT 1,
                FOREIGN KEY (quotation_id) REFERENCES quotations (id) ON DELETE CASCADE,
                FOREIGN KEY (item_type_id) REFERENCES item_types (id)
            )
        ''')
        
        # Contracts table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS contracts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                quotation_id INTEGER UNIQUE NOT NULL,
                contract_number TEXT UNIQUE NOT NULL,
                signature_status TEXT DEFAULT 'Pending' CHECK (signature_status IN ('Pending', 'Signed', 'Cancelled')),
                signed_date DATE,
                contract_date DATE NOT NULL,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (quotation_id) REFERENCES quotations (id)
            )
        ''')
        
        # Manufacturing tracker table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS manufacturing (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                contract_id INTEGER NOT NULL,
                status TEXT DEFAULT 'Pending' CHECK (status IN ('Pending', 'In Progress', 'Completed')),
                start_date DATE,
                completion_date DATE,
                notes TEXT,
                cutting_instructions TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (contract_id) REFERENCES contracts (id)
            )
        ''')
        
        # Installation scheduler table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS installations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                contract_id INTEGER NOT NULL,
                team_assigned TEXT,
                scheduled_date DATE,
                completion_date DATE,
                status TEXT DEFAULT 'Scheduled' CHECK (status IN ('Scheduled', 'In Progress', 'Completed', 'Cancelled')),
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (contract_id) REFERENCES contracts (id)
            )
        ''')
        
        # Inventory table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inventory (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                item_name TEXT NOT NULL,
                item_type TEXT NOT NULL CHECK (item_type IN ('Smart Film Roll', 'Device', 'Remote')),
                current_stock INTEGER NOT NULL DEFAULT 0,
                min_stock_alert INTEGER DEFAULT 10,
                unit TEXT DEFAULT 'pcs',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Inventory transactions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inventory_transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                inventory_id INTEGER NOT NULL,
                transaction_type TEXT NOT NULL CHECK (transaction_type IN ('Add', 'Remove', 'Adjust')),
                quantity INTEGER NOT NULL,
                reason TEXT,
                user_id INTEGER,
                transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (inventory_id) REFERENCES inventory (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        
        # Create default admin user and sample data
        self.create_default_data()
    
    def create_default_data(self):
        """Create default admin user and sample item types"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Check if admin user exists
        cursor.execute("SELECT id FROM users WHERE username = 'admin'")
        if not cursor.fetchone():
            # Create default admin user (password: admin123)
            import hashlib
            password_hash = hashlib.sha256("admin123".encode()).hexdigest()
            cursor.execute('''
                INSERT INTO users (username, password_hash, role, full_name, email)
                VALUES (?, ?, ?, ?, ?)
            ''', ('admin', password_hash, 'Admin', 'System Administrator', '<EMAIL>'))
        
        # Create default item types
        default_items = [
            ('Smart Film', 'Switchable smart glass film'),
            ('Control Device', 'Electronic control unit'),
            ('Remote Control', 'Wireless remote controller'),
            ('Power Supply', 'Power supply unit'),
            ('Installation Kit', 'Complete installation package')
        ]
        
        for item_name, item_desc in default_items:
            cursor.execute('''
                INSERT OR IGNORE INTO item_types (name, description)
                VALUES (?, ?)
            ''', (item_name, item_desc))
        
        # Create default inventory items
        default_inventory = [
            ('Smart Film Roll - Standard', 'Smart Film Roll', 50, 10, 'sqm'),
            ('Smart Film Roll - Premium', 'Smart Film Roll', 30, 5, 'sqm'),
            ('Control Device - Basic', 'Device', 25, 5, 'pcs'),
            ('Control Device - Advanced', 'Device', 15, 3, 'pcs'),
            ('Remote Control - Standard', 'Remote', 40, 10, 'pcs'),
            ('Remote Control - Premium', 'Remote', 20, 5, 'pcs')
        ]
        
        for item_name, item_type, stock, min_alert, unit in default_inventory:
            cursor.execute('''
                INSERT OR IGNORE INTO inventory (item_name, item_type, current_stock, min_stock_alert, unit)
                VALUES (?, ?, ?, ?, ?)
            ''', (item_name, item_type, stock, min_alert, unit))
        
        conn.commit()
        conn.close()
    
    def execute_query(self, query: str, params: tuple = ()) -> List[Dict[str, Any]]:
        """Execute a SELECT query and return results as list of dictionaries"""
        conn = self.get_connection()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute(query, params)
        results = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return results
    
    def execute_update(self, query: str, params: tuple = ()) -> int:
        """Execute an INSERT/UPDATE/DELETE query and return affected rows"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute(query, params)
        affected_rows = cursor.rowcount
        conn.commit()
        conn.close()
        return affected_rows
    
    def get_last_insert_id(self, query: str, params: tuple = ()) -> int:
        """Execute an INSERT query and return the last inserted ID"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute(query, params)
        last_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return last_id
