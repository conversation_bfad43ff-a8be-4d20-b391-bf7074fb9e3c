"""
Quotations Management Module
"""

import tkinter as tk
from tkinter import messagebox, ttk, filedialog
from datetime import datetime
import os
import sys

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from database.models import DatabaseManager
except ImportError:
    # Fallback for when running as standalone
    import sqlite3

class QuotationsManager:
    """Handles quotation operations"""
    
    def __init__(self, db: DatabaseManager):
        self.db = db
    
    def create_quotation(self, data: dict, user_id: int) -> int:
        """Create a new quotation"""
        try:
            # Calculate total
            subtotal = data['area_sqm'] * data['price_per_sqm']
            discount_amount = subtotal * (data['discount'] / 100)
            total = subtotal - discount_amount
            
            # Insert quotation
            quotation_id = self.db.get_last_insert_id(
                """INSERT INTO quotations 
                   (client_name, phone, email, city, area_sqm, num_devices, 
                    price_per_sqm, discount, total_amount, created_by)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (data['client_name'], data['phone'], data['email'], data['city'],
                 data['area_sqm'], data['num_devices'], data['price_per_sqm'],
                 data['discount'], total, user_id)
            )
            
            # Insert quotation items
            for item_type_id in data['item_types']:
                self.db.execute_update(
                    "INSERT INTO quotation_items (quotation_id, item_type_id) VALUES (?, ?)",
                    (quotation_id, item_type_id)
                )
            
            return quotation_id
        except Exception as e:
            messagebox.showerror("Error", f"Failed to create quotation: {str(e)}")
            return None
    
    def update_quotation(self, quotation_id: int, data: dict) -> bool:
        """Update an existing quotation"""
        try:
            # Calculate total
            subtotal = data['area_sqm'] * data['price_per_sqm']
            discount_amount = subtotal * (data['discount'] / 100)
            total = subtotal - discount_amount
            
            # Update quotation
            self.db.execute_update(
                """UPDATE quotations SET 
                   client_name = ?, phone = ?, email = ?, city = ?, area_sqm = ?,
                   num_devices = ?, price_per_sqm = ?, discount = ?, total_amount = ?,
                   updated_at = CURRENT_TIMESTAMP
                   WHERE id = ?""",
                (data['client_name'], data['phone'], data['email'], data['city'],
                 data['area_sqm'], data['num_devices'], data['price_per_sqm'],
                 data['discount'], total, quotation_id)
            )
            
            # Update quotation items
            self.db.execute_update("DELETE FROM quotation_items WHERE quotation_id = ?", (quotation_id,))
            for item_type_id in data['item_types']:
                self.db.execute_update(
                    "INSERT INTO quotation_items (quotation_id, item_type_id) VALUES (?, ?)",
                    (quotation_id, item_type_id)
                )
            
            return True
        except Exception as e:
            messagebox.showerror("Error", f"Failed to update quotation: {str(e)}")
            return False
    
    def get_quotations(self, status_filter=None) -> list:
        """Get all quotations with optional status filter"""
        query = """
            SELECT q.*, u.full_name as created_by_name
            FROM quotations q
            LEFT JOIN users u ON q.created_by = u.id
        """
        params = ()
        
        if status_filter:
            query += " WHERE q.status = ?"
            params = (status_filter,)
        
        query += " ORDER BY q.created_at DESC"
        
        return self.db.execute_query(query, params)
    
    def get_quotation_by_id(self, quotation_id: int) -> dict:
        """Get quotation by ID with items"""
        quotations = self.db.execute_query(
            """SELECT q.*, u.full_name as created_by_name
               FROM quotations q
               LEFT JOIN users u ON q.created_by = u.id
               WHERE q.id = ?""",
            (quotation_id,)
        )
        
        if not quotations:
            return None
        
        quotation = quotations[0]
        
        # Get quotation items
        items = self.db.execute_query(
            """SELECT qi.*, it.name as item_name
               FROM quotation_items qi
               JOIN item_types it ON qi.item_type_id = it.id
               WHERE qi.quotation_id = ?""",
            (quotation_id,)
        )
        
        quotation['items'] = items
        return quotation
    
    def update_quotation_status(self, quotation_id: int, status: str) -> bool:
        """Update quotation status"""
        try:
            self.db.execute_update(
                "UPDATE quotations SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
                (status, quotation_id)
            )
            return True
        except Exception as e:
            messagebox.showerror("Error", f"Failed to update status: {str(e)}")
            return False
    
    def delete_quotation(self, quotation_id: int) -> bool:
        """Delete a quotation"""
        try:
            # Check if quotation has associated contract
            contracts = self.db.execute_query("SELECT id FROM contracts WHERE quotation_id = ?", (quotation_id,))
            if contracts:
                messagebox.showerror("Error", "Cannot delete quotation with associated contract")
                return False
            
            self.db.execute_update("DELETE FROM quotations WHERE id = ?", (quotation_id,))
            return True
        except Exception as e:
            messagebox.showerror("Error", f"Failed to delete quotation: {str(e)}")
            return False
    
    def get_item_types(self) -> list:
        """Get all active item types"""
        return self.db.execute_query("SELECT * FROM item_types WHERE is_active = 1 ORDER BY name")

class QuotationsModule:
    """Quotations module UI"""
    
    def __init__(self, parent, db: DatabaseManager, current_user: dict):
        self.parent = parent
        self.db = db
        self.current_user = current_user
        self.quotations_manager = QuotationsManager(db)
        self.quotations_tree = None
        self.status_filter_var = None
        
        self.create_interface()
    
    def create_interface(self):
        """Create quotations interface"""
        # Header
        header_frame = StyledFrame(self.parent)
        header_frame.pack(fill='x', padx=20, pady=(20, 10))
        
        title_label = StyledLabel(header_frame, text="Quotations Management", style_type='heading')
        title_label.pack(side='left')
        
        # Header buttons
        btn_frame = StyledFrame(header_frame)
        btn_frame.pack(side='right')
        
        new_btn = AnimatedButton(btn_frame, text="New Quotation", command=self.new_quotation)
        new_btn.pack(side='left', padx=(0, 10))
        
        refresh_btn = AnimatedButton(btn_frame, text="Refresh", command=self.load_quotations, style_type='secondary')
        refresh_btn.pack(side='left')
        
        # Filters
        filter_frame = StyledFrame(self.parent)
        filter_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        StyledLabel(filter_frame, text="Status Filter:").pack(side='left', padx=(0, 10))
        
        self.status_filter_var = tk.StringVar(value="All")
        status_combo = ttk.Combobox(filter_frame, textvariable=self.status_filter_var,
                                   values=["All", "Draft", "Sent", "Accepted", "Rejected"],
                                   state="readonly", width=15)
        status_combo.pack(side='left', padx=(0, 10))
        status_combo.bind('<<ComboboxSelected>>', lambda e: self.load_quotations())
        
        # Search
        search_frame = SearchEntry(filter_frame, placeholder="Search quotations...")
        search_frame.pack(side='right')
        
        # Quotations list
        list_frame = StyledFrame(self.parent, style_type='card')
        list_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Treeview
        columns = ('ID', 'Client', 'Phone', 'City', 'Area (sqm)', 'Devices', 'Total', 'Status', 'Created')
        self.quotations_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        for col in columns:
            self.quotations_tree.heading(col, text=col)
        
        # Column widths
        self.quotations_tree.column('ID', width=50)
        self.quotations_tree.column('Client', width=150)
        self.quotations_tree.column('Phone', width=120)
        self.quotations_tree.column('City', width=100)
        self.quotations_tree.column('Area (sqm)', width=80)
        self.quotations_tree.column('Devices', width=70)
        self.quotations_tree.column('Total', width=100)
        self.quotations_tree.column('Status', width=80)
        self.quotations_tree.column('Created', width=100)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.quotations_tree.yview)
        self.quotations_tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack treeview and scrollbar
        self.quotations_tree.pack(side='left', fill='both', expand=True, padx=(10, 0), pady=10)
        scrollbar.pack(side='right', fill='y', pady=10)
        
        # Context menu
        self.create_context_menu()
        
        # Buttons
        buttons_frame = StyledFrame(self.parent)
        buttons_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        view_btn = AnimatedButton(buttons_frame, text="View", command=self.view_quotation)
        view_btn.pack(side='left', padx=(0, 10))
        
        edit_btn = AnimatedButton(buttons_frame, text="Edit", command=self.edit_quotation)
        edit_btn.pack(side='left', padx=(0, 10))
        
        export_btn = AnimatedButton(buttons_frame, text="Export PDF", command=self.export_pdf, style_type='secondary')
        export_btn.pack(side='left', padx=(0, 10))
        
        delete_btn = AnimatedButton(buttons_frame, text="Delete", command=self.delete_quotation, style_type='secondary')
        delete_btn.pack(side='left', padx=(0, 10))
        
        # Status buttons
        status_frame = StyledFrame(buttons_frame)
        status_frame.pack(side='right')
        
        sent_btn = AnimatedButton(status_frame, text="Mark as Sent", command=lambda: self.update_status('Sent'), style_type='secondary')
        sent_btn.pack(side='left', padx=(0, 5))
        
        accepted_btn = AnimatedButton(status_frame, text="Mark as Accepted", command=lambda: self.update_status('Accepted'), style_type='secondary')
        accepted_btn.pack(side='left', padx=(0, 5))
        
        rejected_btn = AnimatedButton(status_frame, text="Mark as Rejected", command=lambda: self.update_status('Rejected'), style_type='secondary')
        rejected_btn.pack(side='left')
        
        # Load quotations
        self.load_quotations()
        
        # Bind double-click to view
        self.quotations_tree.bind('<Double-1>', lambda e: self.view_quotation())
    
    def create_context_menu(self):
        """Create context menu for quotations tree"""
        self.context_menu = tk.Menu(self.parent, tearoff=0)
        self.context_menu.add_command(label="View", command=self.view_quotation)
        self.context_menu.add_command(label="Edit", command=self.edit_quotation)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Export PDF", command=self.export_pdf)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Mark as Sent", command=lambda: self.update_status('Sent'))
        self.context_menu.add_command(label="Mark as Accepted", command=lambda: self.update_status('Accepted'))
        self.context_menu.add_command(label="Mark as Rejected", command=lambda: self.update_status('Rejected'))
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Delete", command=self.delete_quotation)
        
        self.quotations_tree.bind('<Button-3>', self.show_context_menu)
    
    def show_context_menu(self, event):
        """Show context menu"""
        # Select item under cursor
        item = self.quotations_tree.identify_row(event.y)
        if item:
            self.quotations_tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)
    
    def load_quotations(self):
        """Load quotations into the tree"""
        # Clear existing items
        for item in self.quotations_tree.get_children():
            self.quotations_tree.delete(item)
        
        # Get filter
        status_filter = self.status_filter_var.get() if self.status_filter_var.get() != "All" else None
        
        # Load quotations
        quotations = self.quotations_manager.get_quotations(status_filter)
        
        for quotation in quotations:
            created_date = quotation['created_at'][:10] if quotation['created_at'] else ""
            total_formatted = f"${quotation['total_amount']:,.2f}"
            
            self.quotations_tree.insert('', 'end', values=(
                quotation['id'],
                quotation['client_name'],
                quotation['phone'] or "",
                quotation['city'] or "",
                f"{quotation['area_sqm']:.1f}",
                quotation['num_devices'],
                total_formatted,
                quotation['status'],
                created_date
            ))
    
    def get_selected_quotation_id(self):
        """Get selected quotation ID"""
        selection = self.quotations_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a quotation")
            return None
        
        item = self.quotations_tree.item(selection[0])
        return item['values'][0]
    
    def new_quotation(self):
        """Open new quotation dialog"""
        QuotationEditDialog(self.parent, self.quotations_manager, None, self.current_user, self.load_quotations)
    
    def view_quotation(self):
        """View quotation details"""
        quotation_id = self.get_selected_quotation_id()
        if quotation_id:
            QuotationViewDialog(self.parent, self.quotations_manager, quotation_id)
    
    def edit_quotation(self):
        """Edit selected quotation"""
        quotation_id = self.get_selected_quotation_id()
        if quotation_id:
            quotation = self.quotations_manager.get_quotation_by_id(quotation_id)
            if quotation:
                QuotationEditDialog(self.parent, self.quotations_manager, quotation, self.current_user, self.load_quotations)
    
    def update_status(self, status):
        """Update quotation status"""
        quotation_id = self.get_selected_quotation_id()
        if quotation_id:
            if self.quotations_manager.update_quotation_status(quotation_id, status):
                self.load_quotations()
                messagebox.showinfo("Success", f"Quotation status updated to {status}")
    
    def delete_quotation(self):
        """Delete selected quotation"""
        quotation_id = self.get_selected_quotation_id()
        if quotation_id:
            if messagebox.askyesno("Confirm Delete", "Are you sure you want to delete this quotation?"):
                if self.quotations_manager.delete_quotation(quotation_id):
                    self.load_quotations()
                    messagebox.showinfo("Success", "Quotation deleted successfully")
    
    def export_pdf(self):
        """Export quotation to PDF"""
        quotation_id = self.get_selected_quotation_id()
        if quotation_id:
            quotation = self.quotations_manager.get_quotation_by_id(quotation_id)
            if quotation:
                PDFExporter.export_quotation(quotation)

class QuotationEditDialog:
    """Dialog for creating/editing quotations"""

    def __init__(self, parent, quotations_manager: QuotationsManager, quotation_data: dict, current_user: dict, callback):
        self.parent = parent
        self.quotations_manager = quotations_manager
        self.quotation_data = quotation_data
        self.current_user = current_user
        self.callback = callback
        self.window = None
        self.item_types_vars = {}

        self.create_dialog()

    def create_dialog(self):
        """Create quotation edit dialog"""
        self.window = tk.Toplevel(self.parent)
        title = "Edit Quotation" if self.quotation_data else "New Quotation"
        self.window.title(title)
        self.window.geometry("600x700")
        self.window.configure(bg=COLORS['background'])
        self.window.transient(self.parent)
        self.window.grab_set()

        # Center the dialog
        self.window.update_idletasks()
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - (600 // 2)
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - (700 // 2)
        self.window.geometry(f"600x700+{x}+{y}")

        # Main frame with scrollbar
        main_canvas = tk.Canvas(self.window, bg=COLORS['background'])
        scrollbar = ttk.Scrollbar(self.window, orient="vertical", command=main_canvas.yview)
        scrollable_frame = StyledFrame(main_canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: main_canvas.configure(scrollregion=main_canvas.bbox("all"))
        )

        main_canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        main_canvas.configure(yscrollcommand=scrollbar.set)

        # Title
        title_label = StyledLabel(scrollable_frame, text=title, style_type='heading')
        title_label.pack(pady=(20, 30))

        # Client Information Section
        client_frame = StyledFrame(scrollable_frame, style_type='card')
        client_frame.pack(fill='x', padx=20, pady=(0, 20))

        StyledLabel(client_frame, text="Client Information", style_type='subheading').pack(pady=(15, 10))

        # Client Name
        StyledLabel(client_frame, text="Client Name *:").pack(anchor='w', padx=20, pady=(0, 5))
        self.client_name_entry = StyledEntry(client_frame, width=40)
        self.client_name_entry.pack(padx=20, pady=(0, 10))

        # Phone and Email row
        contact_frame = StyledFrame(client_frame)
        contact_frame.pack(fill='x', padx=20, pady=(0, 10))

        # Phone
        phone_frame = StyledFrame(contact_frame)
        phone_frame.pack(side='left', fill='x', expand=True, padx=(0, 10))
        StyledLabel(phone_frame, text="Phone:").pack(anchor='w')
        self.phone_entry = StyledEntry(phone_frame, width=20)
        self.phone_entry.pack(fill='x')

        # Email
        email_frame = StyledFrame(contact_frame)
        email_frame.pack(side='left', fill='x', expand=True)
        StyledLabel(email_frame, text="Email:").pack(anchor='w')
        self.email_entry = StyledEntry(email_frame, width=20)
        self.email_entry.pack(fill='x')

        # City
        StyledLabel(client_frame, text="City:").pack(anchor='w', padx=20, pady=(0, 5))
        self.city_entry = StyledEntry(client_frame, width=40)
        self.city_entry.pack(padx=20, pady=(0, 15))

        # Project Details Section
        project_frame = StyledFrame(scrollable_frame, style_type='card')
        project_frame.pack(fill='x', padx=20, pady=(0, 20))

        StyledLabel(project_frame, text="Project Details", style_type='subheading').pack(pady=(15, 10))

        # Item Types
        StyledLabel(project_frame, text="Item Types *:").pack(anchor='w', padx=20, pady=(0, 5))
        items_frame = StyledFrame(project_frame, style_type='card')
        items_frame.pack(fill='x', padx=20, pady=(0, 10))

        # Load item types
        item_types = self.quotations_manager.get_item_types()
        for item_type in item_types:
            var = tk.BooleanVar()
            self.item_types_vars[item_type['id']] = var
            cb = tk.Checkbutton(items_frame, text=item_type['name'], variable=var,
                               bg=COLORS['background'], fg=COLORS['text_primary'], font=FONTS['default'])
            cb.pack(anchor='w', padx=10, pady=2)

        # Area and Devices row
        specs_frame = StyledFrame(project_frame)
        specs_frame.pack(fill='x', padx=20, pady=(0, 10))

        # Area
        area_frame = StyledFrame(specs_frame)
        area_frame.pack(side='left', fill='x', expand=True, padx=(0, 10))
        StyledLabel(area_frame, text="Area (sqm) *:").pack(anchor='w')
        self.area_entry = StyledEntry(area_frame, width=15)
        self.area_entry.pack(fill='x')
        self.area_entry.bind('<KeyRelease>', self.calculate_total)

        # Number of Devices
        devices_frame = StyledFrame(specs_frame)
        devices_frame.pack(side='left', fill='x', expand=True)
        StyledLabel(devices_frame, text="Number of Devices *:").pack(anchor='w')
        self.devices_entry = StyledEntry(devices_frame, width=15)
        self.devices_entry.pack(fill='x')

        # Pricing Section
        pricing_frame = StyledFrame(scrollable_frame, style_type='card')
        pricing_frame.pack(fill='x', padx=20, pady=(0, 20))

        StyledLabel(pricing_frame, text="Pricing", style_type='subheading').pack(pady=(15, 10))

        # Price per sqm and Discount row
        price_frame = StyledFrame(pricing_frame)
        price_frame.pack(fill='x', padx=20, pady=(0, 10))

        # Price per sqm
        price_sqm_frame = StyledFrame(price_frame)
        price_sqm_frame.pack(side='left', fill='x', expand=True, padx=(0, 10))
        StyledLabel(price_sqm_frame, text="Price per sqm ($) *:").pack(anchor='w')
        self.price_entry = StyledEntry(price_sqm_frame, width=15)
        self.price_entry.pack(fill='x')
        self.price_entry.bind('<KeyRelease>', self.calculate_total)

        # Discount
        discount_frame = StyledFrame(price_frame)
        discount_frame.pack(side='left', fill='x', expand=True)
        StyledLabel(discount_frame, text="Discount (%):").pack(anchor='w')
        self.discount_entry = StyledEntry(discount_frame, width=15)
        self.discount_entry.pack(fill='x')
        self.discount_entry.insert(0, "0")
        self.discount_entry.bind('<KeyRelease>', self.calculate_total)

        # Total display
        total_frame = StyledFrame(pricing_frame)
        total_frame.pack(fill='x', padx=20, pady=(10, 15))

        StyledLabel(total_frame, text="Total Amount:", style_type='subheading').pack(side='left')
        self.total_label = StyledLabel(total_frame, text="$0.00", style_type='subheading', fg=COLORS['primary'])
        self.total_label.pack(side='right')

        # Buttons
        buttons_frame = StyledFrame(scrollable_frame)
        buttons_frame.pack(fill='x', padx=20, pady=(0, 20))

        save_btn = AnimatedButton(buttons_frame, text="Save Quotation", command=self.save_quotation)
        save_btn.pack(side='left', padx=(0, 10))

        cancel_btn = AnimatedButton(buttons_frame, text="Cancel", command=self.window.destroy, style_type='secondary')
        cancel_btn.pack(side='left')

        # Pack canvas and scrollbar
        main_canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Fill form if editing
        if self.quotation_data:
            self.fill_form()

        # Focus on first field
        self.client_name_entry.focus()

    def fill_form(self):
        """Fill form with existing quotation data"""
        data = self.quotation_data

        self.client_name_entry.insert(0, data['client_name'])
        if data['phone']:
            self.phone_entry.insert(0, data['phone'])
        if data['email']:
            self.email_entry.insert(0, data['email'])
        if data['city']:
            self.city_entry.insert(0, data['city'])

        self.area_entry.insert(0, str(data['area_sqm']))
        self.devices_entry.insert(0, str(data['num_devices']))
        self.price_entry.insert(0, str(data['price_per_sqm']))
        self.discount_entry.delete(0, tk.END)
        self.discount_entry.insert(0, str(data['discount']))

        # Set item types
        for item in data['items']:
            if item['item_type_id'] in self.item_types_vars:
                self.item_types_vars[item['item_type_id']].set(True)

        self.calculate_total()

    def calculate_total(self, event=None):
        """Calculate and display total amount"""
        try:
            area = float(self.area_entry.get() or 0)
            price = float(self.price_entry.get() or 0)
            discount = float(self.discount_entry.get() or 0)

            subtotal = area * price
            discount_amount = subtotal * (discount / 100)
            total = subtotal - discount_amount

            self.total_label.config(text=f"${total:,.2f}")
        except ValueError:
            self.total_label.config(text="$0.00")

    def validate_form(self):
        """Validate form data"""
        if not self.client_name_entry.get().strip():
            messagebox.showerror("Error", "Client name is required")
            return False

        try:
            area = float(self.area_entry.get())
            if area <= 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid area (sqm)")
            return False

        try:
            devices = int(self.devices_entry.get())
            if devices <= 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid number of devices")
            return False

        try:
            price = float(self.price_entry.get())
            if price <= 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid price per sqm")
            return False

        try:
            discount = float(self.discount_entry.get() or 0)
            if discount < 0 or discount > 100:
                raise ValueError()
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid discount (0-100%)")
            return False

        # Check if at least one item type is selected
        selected_items = [item_id for item_id, var in self.item_types_vars.items() if var.get()]
        if not selected_items:
            messagebox.showerror("Error", "Please select at least one item type")
            return False

        return True

    def save_quotation(self):
        """Save quotation data"""
        if not self.validate_form():
            return

        # Collect form data
        data = {
            'client_name': self.client_name_entry.get().strip(),
            'phone': self.phone_entry.get().strip() or None,
            'email': self.email_entry.get().strip() or None,
            'city': self.city_entry.get().strip() or None,
            'area_sqm': float(self.area_entry.get()),
            'num_devices': int(self.devices_entry.get()),
            'price_per_sqm': float(self.price_entry.get()),
            'discount': float(self.discount_entry.get() or 0),
            'item_types': [item_id for item_id, var in self.item_types_vars.items() if var.get()]
        }

        if self.quotation_data:
            # Update existing quotation
            if self.quotations_manager.update_quotation(self.quotation_data['id'], data):
                self.callback()
                self.window.destroy()
                messagebox.showinfo("Success", "Quotation updated successfully")
        else:
            # Create new quotation
            quotation_id = self.quotations_manager.create_quotation(data, self.current_user['id'])
            if quotation_id:
                self.callback()
                self.window.destroy()
                messagebox.showinfo("Success", f"Quotation #{quotation_id} created successfully")
