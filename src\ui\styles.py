"""
UI Styles and Theme Configuration for Beyond Smart Glass Tracker
"""

# Color Palette
COLORS = {
    'primary': '#4A90E2',      # Light blue
    'primary_dark': '#357ABD',  # Darker blue for hover
    'secondary': '#F8F9FA',     # Light gray
    'background': '#FFFFFF',    # White
    'surface': '#F5F7FA',      # Very light blue-gray
    'text_primary': '#2C3E50',  # Dark gray
    'text_secondary': '#7F8C8D', # Medium gray
    'success': '#27AE60',       # Green
    'warning': '#F39C12',       # Orange
    'error': '#E74C3C',         # Red
    'border': '#E1E8ED',        # Light border
    'hover': '#EBF3FD'          # Light blue hover
}

# Font Configuration
FONTS = {
    'default': ('Segoe UI', 10),
    'heading': ('Segoe UI', 14, 'bold'),
    'subheading': ('Segoe UI', 12, 'bold'),
    'small': ('Segoe UI', 9),
    'button': ('Segoe UI', 10, 'bold')
}

# Widget Styles
BUTTON_STYLE = {
    'font': FONTS['button'],
    'bg': COLORS['primary'],
    'fg': 'white',
    'relief': 'flat',
    'bd': 0,
    'padx': 20,
    'pady': 8,
    'cursor': 'hand2'
}

BUTTON_SECONDARY_STYLE = {
    'font': FONTS['button'],
    'bg': COLORS['secondary'],
    'fg': COLORS['text_primary'],
    'relief': 'flat',
    'bd': 1,
    'padx': 20,
    'pady': 8,
    'cursor': 'hand2'
}

ENTRY_STYLE = {
    'font': FONTS['default'],
    'bg': 'white',
    'fg': COLORS['text_primary'],
    'relief': 'solid',
    'bd': 1,
    'highlightthickness': 2,
    'highlightcolor': COLORS['primary'],
    'highlightbackground': COLORS['border']
}

LABEL_STYLE = {
    'font': FONTS['default'],
    'bg': COLORS['background'],
    'fg': COLORS['text_primary']
}

FRAME_STYLE = {
    'bg': COLORS['background'],
    'relief': 'flat',
    'bd': 0
}

CARD_STYLE = {
    'bg': 'white',
    'relief': 'solid',
    'bd': 1,
    'highlightbackground': COLORS['border'],
    'highlightthickness': 1
}

# Layout Constants
PADDING = {
    'small': 5,
    'medium': 10,
    'large': 20,
    'xlarge': 30
}

WINDOW_CONFIG = {
    'min_width': 1200,
    'min_height': 800,
    'bg': COLORS['surface']
}
