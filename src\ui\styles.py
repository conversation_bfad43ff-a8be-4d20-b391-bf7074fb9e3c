"""
UI Styles and Theme Configuration for Beyond Smart Glass Tracker
"""

# Color Palette - Modern SultanTrack Style
COLORS = {
    'primary': '#4F46E5',       # Modern indigo
    'primary_dark': '#3730A3',  # Darker indigo for hover
    'primary_light': '#6366F1', # Light indigo
    'secondary': '#F8FAFC',     # Very light gray
    'background': '#F1F5F9',    # Light blue-gray background
    'surface': '#FFFFFF',       # White cards/surfaces
    'sidebar': '#1E293B',       # Dark sidebar
    'sidebar_hover': '#334155', # Sidebar hover
    'text_primary': '#0F172A',  # Very dark text
    'text_secondary': '#64748B', # Medium gray text
    'text_light': '#94A3B8',    # Light gray text
    'success': '#10B981',       # Modern green
    'warning': '#F59E0B',       # Modern orange
    'error': '#EF4444',         # Modern red
    'info': '#3B82F6',          # Modern blue
    'border': '#E2E8F0',        # Light border
    'hover': '#F1F5F9',         # Light hover
    'card_shadow': '#0F172A10'  # Card shadow
}

# Font Configuration
FONTS = {
    'default': ('Segoe UI', 10),
    'heading': ('Segoe UI', 14, 'bold'),
    'subheading': ('Segoe UI', 12, 'bold'),
    'small': ('Segoe UI', 9),
    'button': ('Segoe UI', 10, 'bold')
}

# Widget Styles - Modern Design
BUTTON_STYLE = {
    'font': FONTS['button'],
    'bg': COLORS['primary'],
    'fg': 'white',
    'relief': 'flat',
    'bd': 0,
    'padx': 24,
    'pady': 12,
    'cursor': 'hand2',
    'borderwidth': 0
}

BUTTON_SECONDARY_STYLE = {
    'font': FONTS['button'],
    'bg': COLORS['surface'],
    'fg': COLORS['text_primary'],
    'relief': 'flat',
    'bd': 1,
    'padx': 24,
    'pady': 12,
    'cursor': 'hand2',
    'highlightthickness': 1,
    'highlightcolor': COLORS['border']
}

ENTRY_STYLE = {
    'font': FONTS['default'],
    'bg': 'white',
    'fg': COLORS['text_primary'],
    'relief': 'solid',
    'bd': 1,
    'highlightthickness': 2,
    'highlightcolor': COLORS['primary'],
    'highlightbackground': COLORS['border']
}

LABEL_STYLE = {
    'font': FONTS['default'],
    'bg': COLORS['background'],
    'fg': COLORS['text_primary']
}

FRAME_STYLE = {
    'bg': COLORS['background'],
    'relief': 'flat',
    'bd': 0
}

CARD_STYLE = {
    'bg': COLORS['surface'],
    'relief': 'flat',
    'bd': 0,
    'highlightbackground': COLORS['border'],
    'highlightthickness': 1
}

SIDEBAR_STYLE = {
    'bg': COLORS['sidebar'],
    'fg': 'white',
    'font': FONTS['body'],
    'relief': 'flat',
    'bd': 0
}

SIDEBAR_BUTTON_STYLE = {
    'font': FONTS['body'],
    'bg': COLORS['sidebar'],
    'fg': 'white',
    'relief': 'flat',
    'bd': 0,
    'padx': 24,
    'pady': 16,
    'cursor': 'hand2',
    'anchor': 'w'
}

SIDEBAR_BUTTON_ACTIVE_STYLE = {
    'font': FONTS['body'],
    'bg': COLORS['primary'],
    'fg': 'white',
    'relief': 'flat',
    'bd': 0,
    'padx': 24,
    'pady': 16,
    'cursor': 'hand2',
    'anchor': 'w'
}

# Layout Constants
PADDING = {
    'small': 5,
    'medium': 10,
    'large': 20,
    'xlarge': 30
}

WINDOW_CONFIG = {
    'min_width': 1200,
    'min_height': 800,
    'bg': COLORS['surface']
}
