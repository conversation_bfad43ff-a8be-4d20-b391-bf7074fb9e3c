"""
Authentication and User Management Module
"""

import tkinter as tk
from tkinter import messagebox, ttk
import hashlib
import re
from datetime import datetime

from ..ui.base_widgets import *
from ..ui.styles import *
from ..database.models import DatabaseManager

class AuthManager:
    """Handles user authentication and management"""
    
    def __init__(self, db: DatabaseManager):
        self.db = db
    
    def authenticate_user(self, username: str, password: str) -> dict:
        """Authenticate user credentials"""
        if not username or not password:
            return None
        
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        users = self.db.execute_query(
            "SELECT * FROM users WHERE username = ? AND password_hash = ? AND is_active = 1",
            (username, password_hash)
        )
        
        return users[0] if users else None
    
    def create_user(self, username: str, password: str, role: str, full_name: str, email: str = None) -> bool:
        """Create a new user"""
        try:
            # Validate input
            if not self.validate_user_data(username, password, role, full_name, email):
                return False
            
            # Check if username already exists
            existing = self.db.execute_query("SELECT id FROM users WHERE username = ?", (username,))
            if existing:
                messagebox.showerror("Error", "Username already exists")
                return False
            
            # Hash password
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            
            # Insert user
            self.db.execute_update(
                "INSERT INTO users (username, password_hash, role, full_name, email) VALUES (?, ?, ?, ?, ?)",
                (username, password_hash, role, full_name, email)
            )
            
            return True
        except Exception as e:
            messagebox.showerror("Error", f"Failed to create user: {str(e)}")
            return False
    
    def update_user(self, user_id: int, username: str, role: str, full_name: str, email: str = None, new_password: str = None) -> bool:
        """Update user information"""
        try:
            # Validate input
            if not self.validate_user_data(username, new_password or "dummy", role, full_name, email, user_id):
                return False
            
            # Check if username already exists for other users
            existing = self.db.execute_query("SELECT id FROM users WHERE username = ? AND id != ?", (username, user_id))
            if existing:
                messagebox.showerror("Error", "Username already exists")
                return False
            
            # Update user
            if new_password:
                password_hash = hashlib.sha256(new_password.encode()).hexdigest()
                self.db.execute_update(
                    "UPDATE users SET username = ?, password_hash = ?, role = ?, full_name = ?, email = ? WHERE id = ?",
                    (username, password_hash, role, full_name, email, user_id)
                )
            else:
                self.db.execute_update(
                    "UPDATE users SET username = ?, role = ?, full_name = ?, email = ? WHERE id = ?",
                    (username, role, full_name, email, user_id)
                )
            
            return True
        except Exception as e:
            messagebox.showerror("Error", f"Failed to update user: {str(e)}")
            return False
    
    def deactivate_user(self, user_id: int) -> bool:
        """Deactivate a user"""
        try:
            self.db.execute_update("UPDATE users SET is_active = 0 WHERE id = ?", (user_id,))
            return True
        except Exception as e:
            messagebox.showerror("Error", f"Failed to deactivate user: {str(e)}")
            return False
    
    def activate_user(self, user_id: int) -> bool:
        """Activate a user"""
        try:
            self.db.execute_update("UPDATE users SET is_active = 1 WHERE id = ?", (user_id,))
            return True
        except Exception as e:
            messagebox.showerror("Error", f"Failed to activate user: {str(e)}")
            return False
    
    def get_all_users(self) -> list:
        """Get all users"""
        return self.db.execute_query("SELECT * FROM users ORDER BY username")
    
    def validate_user_data(self, username: str, password: str, role: str, full_name: str, email: str = None, user_id: int = None) -> bool:
        """Validate user data"""
        # Username validation
        if not username or len(username) < 3:
            messagebox.showerror("Error", "Username must be at least 3 characters long")
            return False
        
        if not re.match("^[a-zA-Z0-9_]+$", username):
            messagebox.showerror("Error", "Username can only contain letters, numbers, and underscores")
            return False
        
        # Password validation (only for new users or password changes)
        if password != "dummy":  # dummy is used for updates without password change
            if not password or len(password) < 6:
                messagebox.showerror("Error", "Password must be at least 6 characters long")
                return False
        
        # Role validation
        if role not in ['Admin', 'Sales']:
            messagebox.showerror("Error", "Role must be either Admin or Sales")
            return False
        
        # Full name validation
        if not full_name or len(full_name.strip()) < 2:
            messagebox.showerror("Error", "Full name must be at least 2 characters long")
            return False
        
        # Email validation (optional)
        if email and email.strip():
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, email):
                messagebox.showerror("Error", "Please enter a valid email address")
                return False
        
        return True

class UserManagementWindow:
    """User management interface for admin users"""
    
    def __init__(self, parent, auth_manager: AuthManager, current_user: dict):
        self.parent = parent
        self.auth_manager = auth_manager
        self.current_user = current_user
        self.window = None
        self.users_tree = None
        
        if current_user['role'] != 'Admin':
            messagebox.showerror("Access Denied", "Only administrators can manage users")
            return
        
        self.create_window()
    
    def create_window(self):
        """Create user management window"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("User Management")
        self.window.geometry("800x600")
        self.window.configure(bg=COLORS['background'])
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # Header
        header_frame = StyledFrame(self.window)
        header_frame.pack(fill='x', padx=20, pady=(20, 10))
        
        title_label = StyledLabel(header_frame, text="User Management", style_type='heading')
        title_label.pack(side='left')
        
        add_btn = AnimatedButton(header_frame, text="Add User", command=self.add_user)
        add_btn.pack(side='right')
        
        # Users list
        list_frame = StyledFrame(self.window, style_type='card')
        list_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Treeview for users
        columns = ('ID', 'Username', 'Full Name', 'Role', 'Email', 'Status', 'Created')
        self.users_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        self.users_tree.heading('ID', text='ID')
        self.users_tree.heading('Username', text='Username')
        self.users_tree.heading('Full Name', text='Full Name')
        self.users_tree.heading('Role', text='Role')
        self.users_tree.heading('Email', text='Email')
        self.users_tree.heading('Status', text='Status')
        self.users_tree.heading('Created', text='Created')
        
        # Column widths
        self.users_tree.column('ID', width=50)
        self.users_tree.column('Username', width=100)
        self.users_tree.column('Full Name', width=150)
        self.users_tree.column('Role', width=80)
        self.users_tree.column('Email', width=150)
        self.users_tree.column('Status', width=80)
        self.users_tree.column('Created', width=100)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.users_tree.yview)
        self.users_tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack treeview and scrollbar
        self.users_tree.pack(side='left', fill='both', expand=True, padx=(10, 0), pady=10)
        scrollbar.pack(side='right', fill='y', pady=10)
        
        # Buttons frame
        buttons_frame = StyledFrame(self.window)
        buttons_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        edit_btn = AnimatedButton(buttons_frame, text="Edit User", command=self.edit_user)
        edit_btn.pack(side='left', padx=(0, 10))
        
        toggle_btn = AnimatedButton(buttons_frame, text="Toggle Status", command=self.toggle_user_status, style_type='secondary')
        toggle_btn.pack(side='left', padx=(0, 10))
        
        close_btn = AnimatedButton(buttons_frame, text="Close", command=self.window.destroy, style_type='secondary')
        close_btn.pack(side='right')
        
        # Load users
        self.load_users()
        
        # Bind double-click to edit
        self.users_tree.bind('<Double-1>', lambda e: self.edit_user())
    
    def load_users(self):
        """Load users into the treeview"""
        # Clear existing items
        for item in self.users_tree.get_children():
            self.users_tree.delete(item)
        
        # Load users
        users = self.auth_manager.get_all_users()
        for user in users:
            status = "Active" if user['is_active'] else "Inactive"
            created_date = user['created_at'][:10] if user['created_at'] else ""
            
            self.users_tree.insert('', 'end', values=(
                user['id'],
                user['username'],
                user['full_name'],
                user['role'],
                user['email'] or "",
                status,
                created_date
            ))
    
    def add_user(self):
        """Open add user dialog"""
        UserEditDialog(self.window, self.auth_manager, None, self.load_users)
    
    def edit_user(self):
        """Open edit user dialog"""
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a user to edit")
            return
        
        item = self.users_tree.item(selection[0])
        user_id = item['values'][0]
        
        # Get user data
        users = self.auth_manager.db.execute_query("SELECT * FROM users WHERE id = ?", (user_id,))
        if users:
            UserEditDialog(self.window, self.auth_manager, users[0], self.load_users)
    
    def toggle_user_status(self):
        """Toggle user active status"""
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a user to toggle status")
            return
        
        item = self.users_tree.item(selection[0])
        user_id = item['values'][0]
        current_status = item['values'][5]
        
        # Don't allow deactivating self
        if user_id == self.current_user['id']:
            messagebox.showerror("Error", "You cannot deactivate your own account")
            return
        
        if current_status == "Active":
            if self.auth_manager.deactivate_user(user_id):
                self.load_users()
        else:
            if self.auth_manager.activate_user(user_id):
                self.load_users()

class UserEditDialog:
    """Dialog for adding/editing users"""
    
    def __init__(self, parent, auth_manager: AuthManager, user_data: dict, callback):
        self.parent = parent
        self.auth_manager = auth_manager
        self.user_data = user_data
        self.callback = callback
        self.window = None
        
        self.create_dialog()
    
    def create_dialog(self):
        """Create user edit dialog"""
        self.window = tk.Toplevel(self.parent)
        title = "Edit User" if self.user_data else "Add User"
        self.window.title(title)
        self.window.geometry("400x350")
        self.window.configure(bg=COLORS['background'])
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # Center the dialog
        self.window.update_idletasks()
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - (400 // 2)
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - (350 // 2)
        self.window.geometry(f"400x350+{x}+{y}")
        
        # Main frame
        main_frame = StyledFrame(self.window, style_type='card')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Title
        title_label = StyledLabel(main_frame, text=title, style_type='heading')
        title_label.pack(pady=(20, 30))
        
        # Form fields
        # Username
        StyledLabel(main_frame, text="Username:").pack(anchor='w', padx=40, pady=(0, 5))
        self.username_entry = StyledEntry(main_frame, width=25)
        self.username_entry.pack(padx=40, pady=(0, 15))
        
        # Full Name
        StyledLabel(main_frame, text="Full Name:").pack(anchor='w', padx=40, pady=(0, 5))
        self.fullname_entry = StyledEntry(main_frame, width=25)
        self.fullname_entry.pack(padx=40, pady=(0, 15))
        
        # Email
        StyledLabel(main_frame, text="Email (optional):").pack(anchor='w', padx=40, pady=(0, 5))
        self.email_entry = StyledEntry(main_frame, width=25)
        self.email_entry.pack(padx=40, pady=(0, 15))
        
        # Role
        StyledLabel(main_frame, text="Role:").pack(anchor='w', padx=40, pady=(0, 5))
        self.role_var = tk.StringVar(value="Sales")
        role_frame = StyledFrame(main_frame)
        role_frame.pack(padx=40, pady=(0, 15))
        
        tk.Radiobutton(role_frame, text="Sales", variable=self.role_var, value="Sales",
                      bg=COLORS['background'], fg=COLORS['text_primary'], font=FONTS['default']).pack(side='left')
        tk.Radiobutton(role_frame, text="Admin", variable=self.role_var, value="Admin",
                      bg=COLORS['background'], fg=COLORS['text_primary'], font=FONTS['default']).pack(side='left', padx=(20, 0))
        
        # Password
        password_text = "New Password:" if self.user_data else "Password:"
        StyledLabel(main_frame, text=password_text).pack(anchor='w', padx=40, pady=(0, 5))
        self.password_entry = StyledEntry(main_frame, width=25, show="*")
        self.password_entry.pack(padx=40, pady=(0, 15))
        
        if self.user_data:
            StyledLabel(main_frame, text="(Leave blank to keep current password)", 
                       style_type='small', fg=COLORS['text_secondary']).pack(padx=40, pady=(0, 15))
        
        # Buttons
        buttons_frame = StyledFrame(main_frame)
        buttons_frame.pack(pady=(10, 20))
        
        save_btn = AnimatedButton(buttons_frame, text="Save", command=self.save_user)
        save_btn.pack(side='left', padx=(0, 10))
        
        cancel_btn = AnimatedButton(buttons_frame, text="Cancel", command=self.window.destroy, style_type='secondary')
        cancel_btn.pack(side='left')
        
        # Fill form if editing
        if self.user_data:
            self.username_entry.insert(0, self.user_data['username'])
            self.fullname_entry.insert(0, self.user_data['full_name'])
            if self.user_data['email']:
                self.email_entry.insert(0, self.user_data['email'])
            self.role_var.set(self.user_data['role'])
        
        # Focus on first field
        self.username_entry.focus()
    
    def save_user(self):
        """Save user data"""
        username = self.username_entry.get().strip()
        full_name = self.fullname_entry.get().strip()
        email = self.email_entry.get().strip() or None
        role = self.role_var.get()
        password = self.password_entry.get().strip()
        
        if self.user_data:
            # Update existing user
            if self.auth_manager.update_user(self.user_data['id'], username, role, full_name, email, password or None):
                self.callback()
                self.window.destroy()
        else:
            # Create new user
            if self.auth_manager.create_user(username, password, role, full_name, email):
                self.callback()
                self.window.destroy()
